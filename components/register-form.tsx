"use client"

import type React from "react"
import { useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { authService } from "@/lib/auth-service"
import type { UserRegisterRequest } from "@/lib/api-types"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"

export const RegisterForm: React.FC = () => {
  const [formData, setFormData] = useState<UserRegisterRequest & { confirmPassword?: string }>({
    username: "",
    password: "",
    confirmPassword: "",
    email: "",
    display_name: "",
    invitation_code: "",
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const { toast } = useToast()
  const router = useRouter()

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError("")

    if (!formData.username.trim()) {
      setError("Username is required.")
      toast({ title: "Validation Error", description: "Username is required.", variant: "destructive" })
      setLoading(false)
      return
    }
    if (!formData.password) {
      setError("Password is required.")
      toast({ title: "Validation Error", description: "Password is required.", variant: "destructive" })
      setLoading(false)
      return
    }
    if (formData.password !== formData.confirmPassword) {
      setError("Passwords do not match.")
      toast({ title: "Validation Error", description: "Passwords do not match.", variant: "destructive" })
      setLoading(false)
      return
    }
    if (!formData.email || !formData.email.trim()) {
      setError("Email is required.")
      toast({ title: "Validation Error", description: "Email is required.", variant: "destructive" })
      setLoading(false)
      return
    }
    if (!/\S+@\S+\.\S+/.test(formData.email)) {
      setError("Please enter a valid email address.")
      toast({ title: "Validation Error", description: "Please enter a valid email address.", variant: "destructive" })
      setLoading(false)
      return
    }

    try {
      const { confirmPassword, ...registerData } = formData
      const finalRegisterData: UserRegisterRequest = {
        username: registerData.username,
        password: registerData.password,
        email: registerData.email,
        display_name: registerData.display_name || undefined,
        invitation_code: registerData.invitation_code || undefined,
      }

      const response = await authService.register(finalRegisterData)
      toast({
        title: "Registration Successful",
        description: response.message || "You can now log in.",
      })
      router.push("/login") // Redirect to login page
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Registration failed"
      setError(errorMessage)
      toast({ title: "Registration Failed", description: errorMessage, variant: "destructive" })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Create an Account</CardTitle>
        <CardDescription>Enter your details to register for Processing.</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="username">
              Username <span className="text-red-500">*</span>
            </Label>
            <Input
              id="username"
              name="username"
              type="text"
              value={formData.username}
              onChange={handleChange}
              required
              placeholder="Choose a username"
            />
          </div>
          <div>
            <Label htmlFor="email">
              Email <span className="text-red-500">*</span>
            </Label>
            <Input
              id="email"
              name="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              required
              placeholder="<EMAIL>"
            />
          </div>
          <div>
            <Label htmlFor="password">
              Password <span className="text-red-500">*</span>
            </Label>
            <Input
              id="password"
              name="password"
              type="password"
              value={formData.password}
              onChange={handleChange}
              required
              placeholder="Create a password"
            />
          </div>
          <div>
            <Label htmlFor="confirmPassword">
              Confirm Password <span className="text-red-500">*</span>
            </Label>
            <Input
              id="confirmPassword"
              name="confirmPassword"
              type="password"
              value={formData.confirmPassword}
              onChange={handleChange}
              required
              placeholder="Confirm your password"
            />
          </div>
          <div>
            <Label htmlFor="display_name">Display Name (Optional)</Label>
            <Input
              id="display_name"
              name="display_name"
              type="text"
              value={formData.display_name}
              onChange={handleChange}
              placeholder="How you want to be seen"
            />
          </div>
          <div>
            <Label htmlFor="invitation_code">Invitation Code (Optional)</Label>
            <Input
              id="invitation_code"
              name="invitation_code"
              type="text"
              value={formData.invitation_code}
              onChange={handleChange}
              placeholder="If you have one"
            />
          </div>
          {error && <p className="text-sm text-red-600">{error}</p>}
          <Button type="submit" disabled={loading} className="w-full bg-green-600 hover:bg-green-700">
            {loading ? "Registering..." : "Register"}
          </Button>
        </form>
        <p className="mt-6 text-center text-sm text-gray-600 dark:text-gray-400">
          Already have an account?{" "}
          <Link href="/login" className="font-medium text-green-600 hover:underline dark:text-green-500">
            Log in
          </Link>
        </p>
      </CardContent>
    </Card>
  )
}
