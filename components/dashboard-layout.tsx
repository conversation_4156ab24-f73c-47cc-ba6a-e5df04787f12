"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { useRouter } from "next/navigation" // Import useRouter
import { Sidebar } from "./sidebar"
import { TopNav } from "./top-nav"
// LoginForm is no longer rendered here directly
import { authService } from "@/lib/auth-service"
import { apiClient } from "@/lib/api-client"
import type { UserResponse as AuthUser } from "@/lib/api-types"
import { Toaster } from "@/components/ui/toaster"
import { Layers3 } from "lucide-react"

export function DashboardLayout({ children }: { children: React.ReactNode }) {
  const router = useRouter()
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [currentUser, setCurrentUser] = useState<Partial<AuthUser> | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const checkAuth = async () => {
      setIsLoading(true)
      const authStatus = authService.isAuthenticated()
      if (!authStatus) {
        router.replace("/login") // Redirect to login if not authenticated
        return // Important to return to prevent further execution for unauth users
      }

      setIsAuthenticated(true)
      const user = authService.getCurrentUser()
      setCurrentUser(user)
      const jwt = localStorage.getItem("jwt_token")
      if (jwt) apiClient.setJWTToken(jwt)
      const apiKey = localStorage.getItem("api_key")
      if (apiKey) apiClient.setAPIKey(apiKey)

      try {
        const fullUserResponse = await authService.getCurrentUserInfo()
        if (fullUserResponse.data) {
          setCurrentUser(fullUserResponse.data)
          localStorage.setItem("user_info", JSON.stringify(fullUserResponse.data))
        }
      } catch (error) {
        console.warn("Could not fetch full user details, using localStorage info.", error)
        // If fetching full user info fails and leads to logout, redirect
        if (!authService.isAuthenticated()) {
          // Re-check auth status
          router.replace("/login")
          return
        }
      }
      setIsLoading(false)
    }
    checkAuth()
  }, [router])

  const handleLogout = () => {
    authService.logout()
    setIsAuthenticated(false)
    setCurrentUser(null)
    apiClient.clearAuth()
    router.replace("/login") // Redirect to login on logout
  }

  if (isLoading || !isAuthenticated) {
    // Show loader until auth check is complete AND user is authenticated
    return (
      <div className="flex h-screen items-center justify-center bg-gray-100 dark:bg-gray-900">
        <Layers3 className="h-12 w-12 text-green-600 animate-pulse" />
      </div>
    )
  }

  // If authenticated, render the dashboard layout
  return (
    <div className="flex min-h-screen bg-gray-100 dark:bg-gray-950">
      <Sidebar />
      <div className="flex flex-1 flex-col pl-64">
        <TopNav currentUser={currentUser} onLogout={handleLogout} />
        <main className="flex-1 overflow-y-auto p-6">{children}</main>
      </div>
      <Toaster />
    </div>
  )
}
