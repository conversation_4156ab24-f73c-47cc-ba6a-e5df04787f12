"use client"

import type React from "react"
import { useState, useEffect, useCallback } from "react"
import { tokenService } from "@/lib/token-service"
import type { TokenResponse as APIToken } from "@/lib/api-types"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog"
import { Trash2, PlusCircle, RefreshCw } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { Switch } from "@/components/ui/switch"

export const TokenManager: React.FC = () => {
  const [tokens, setTokens] = useState<APIToken[]>([])
  const [loading, setLoading] = useState(true)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [newTokenData, setNewTokenData] = useState({
    name: "",
    remain_quota: 1000,
    unlimited_quota: false,
  })
  const { toast } = useToast()

  const loadTokens = useCallback(async () => {
    setLoading(true)
    try {
      const response = await tokenService.getTokens({ page: 1, per_page: 100 }) // Fetch more tokens
      setTokens(response.items || []) // API returns items in 'items'
    } catch (error) {
      console.error("Failed to load tokens:", error)
      toast({ title: "Error", description: "Failed to load tokens.", variant: "destructive" })
      setTokens([]) // Ensure tokens is an array on error
    } finally {
      setLoading(false)
    }
  }, [toast])

  useEffect(() => {
    loadTokens()
  }, [loadTokens])

  const handleCreateToken = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newTokenData.name.trim()) {
      toast({ title: "Validation Error", description: "Token name cannot be empty.", variant: "destructive" })
      return
    }
    try {
      await tokenService.createToken({
        name: newTokenData.name,
        remain_quota: newTokenData.unlimited_quota ? 0 : Number(newTokenData.remain_quota),
        unlimited_quota: newTokenData.unlimited_quota,
      })
      setNewTokenData({ name: "", remain_quota: 1000, unlimited_quota: false })
      setIsCreateDialogOpen(false)
      loadTokens()
      toast({ title: "Success", description: "Token created successfully." })
    } catch (error) {
      console.error("Failed to create token:", error)
      toast({ title: "Error", description: "Failed to create token.", variant: "destructive" })
    }
  }

  const handleDeleteToken = async (tokenId: number) => {
    if (confirm("确定要删除这个 Token 吗？")) {
      try {
        await tokenService.deleteToken(tokenId)
        loadTokens()
        toast({ title: "Success", description: "Token deleted successfully." })
      } catch (error) {
        console.error("Failed to delete token:", error)
        toast({ title: "Error", description: "Failed to delete token.", variant: "destructive" })
      }
    }
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-32">
        <RefreshCw className="h-8 w-8 animate-spin text-primary" /> <span className="ml-2">加载中...</span>
      </div>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>API Token 管理</CardTitle>
            <CardDescription>创建和管理您的 API 访问令牌。</CardDescription>
          </div>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <PlusCircle className="mr-2 h-4 w-4" /> 创建新 Token
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>创建新 Token</DialogTitle>
                <DialogDescription>为您的应用或服务创建一个新的 API Token。</DialogDescription>
              </DialogHeader>
              <form onSubmit={handleCreateToken} className="space-y-4 py-4">
                <div>
                  <Label htmlFor="tokenName">Token 名称</Label>
                  <Input
                    id="tokenName"
                    type="text"
                    value={newTokenData.name}
                    onChange={(e) => setNewTokenData({ ...newTokenData, name: e.target.value })}
                    placeholder="例如：My App Token"
                    required
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="unlimitedQuota"
                    checked={newTokenData.unlimited_quota}
                    onCheckedChange={(checked) => setNewTokenData({ ...newTokenData, unlimited_quota: checked })}
                  />
                  <Label htmlFor="unlimitedQuota">无限配额</Label>
                </div>
                {!newTokenData.unlimited_quota && (
                  <div>
                    <Label htmlFor="tokenQuota">初始配额</Label>
                    <Input
                      id="tokenQuota"
                      type="number"
                      value={newTokenData.remain_quota}
                      onChange={(e) =>
                        setNewTokenData({ ...newTokenData, remain_quota: Number.parseInt(e.target.value, 10) || 0 })
                      }
                      required
                    />
                  </div>
                )}
                <DialogFooter>
                  <DialogClose asChild>
                    <Button type="button" variant="outline">
                      取消
                    </Button>
                  </DialogClose>
                  <Button type="submit">创建</Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {tokens.length === 0 ? (
          <p className="text-center text-gray-500 dark:text-gray-400 py-8">
            没有可用的 Token。点击右上角按钮创建一个。
          </p>
        ) : (
          <div className="space-y-4">
            {tokens.map((token) => (
              <Card key={token.id} className="bg-gray-50 dark:bg-gray-800/30">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <CardTitle className="text-lg">{token.name}</CardTitle>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteToken(token.id)}
                      aria-label="Delete token"
                    >
                      <Trash2 className="h-4 w-4 text-red-500" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-2 text-sm">
                  <p className="font-mono bg-gray-100 dark:bg-gray-700 p-2 rounded text-xs break-all">
                    Key: {token.key}
                  </p>
                  <p>
                    状态:{" "}
                    <span className={token.status === 1 ? "text-green-600" : "text-red-600"}>{token.status_text}</span>
                  </p>
                  <p>
                    配额:{" "}
                    {token.unlimited_quota
                      ? "无限"
                      : `${token.remain_quota} / ${token.remain_quota + token.used_quota}`}
                  </p>
                  <p>已用配额: {token.used_quota}</p>
                  <p>请求次数: {token.request_count}</p>
                  <p>创建时间: {new Date(token.created_time).toLocaleString()}</p>
                  {token.last_used_time && <p>最后使用: {new Date(token.last_used_time).toLocaleString()}</p>}
                  {typeof token.expired_time === "string" && new Date(token.expired_time).getTime() > 0 && (
                    <p>过期时间: {new Date(token.expired_time).toLocaleString()}</p>
                  )}
                  {typeof token.expired_time === "number" && token.expired_time !== -1 && (
                    <p>过期时间: {new Date(token.expired_time * 1000).toLocaleString()}</p>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
