"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { LogOut, Coins } from "lucide-react"
import type { UserResponse as AuthUser } from "@/lib/api-types"

interface TopNavProps {
  currentUser: Partial<AuthUser> | null
  onLogout: () => void
}

export function TopNav({ currentUser, onLogout }: TopNavProps) {
  const userInitial = currentUser?.username ? currentUser.username.charAt(0).toUpperCase() : "U"

  return (
    <header className="flex h-16 items-center justify-between border-b border-gray-200 bg-white px-6 dark:border-gray-800 dark:bg-gray-950">
      <div>{/* Placeholder for breadcrumbs or page title if needed */}</div>
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2 rounded-full bg-green-100 px-3 py-1.5 text-sm font-medium text-green-700 dark:bg-green-700/30 dark:text-green-400">
          <Coins className="h-4 w-4" />
          <span>{currentUser?.quota ?? 0} 次额度</span>
        </div>
        <div className="flex items-center gap-3">
          <Avatar className="h-9 w-9">
            <AvatarImage
              src={`https://avatar.vercel.sh/${currentUser?.username}.png`}
              alt={currentUser?.username || "User"}
            />
            <AvatarFallback>{userInitial}</AvatarFallback>
          </Avatar>
          <div>
            <p className="text-sm font-medium text-gray-800 dark:text-gray-100">
              {currentUser?.display_name || currentUser?.username}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">@{currentUser?.username}</p>
          </div>
        </div>
        <Button variant="outline" size="sm" onClick={onLogout}>
          <LogOut className="mr-2 h-4 w-4" />
          退出登录
        </Button>
      </div>
    </header>
  )
}
