"use client"

import type React from "react"
import { useState, useEffect, useCallback } from "react"
import { transcriptionService } from "@/lib/transcription-service"
import type { ModelObject } from "@/lib/api-types"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Textarea } from "@/components/ui/textarea"
import { UploadCloud, AlertTriangle, RefreshCw, Package } from "lucide-react" // Added Package for Batch ID
import { useToast } from "@/hooks/use-toast"
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { TranscriptionTaskList } from "@/components/transcription-task-list"

const MAX_FILE_SIZE = 25 * 1024 * 1024 // 25MB
const ALLOWED_FILE_TYPES = [
  "audio/mpeg",
  "audio/wav",
  "audio/mp4",
  "audio/webm",
  "audio/ogg",
  "video/mp4",
  "video/webm",
]

export const AudioTranscription: React.FC = () => {
  const [file, setFile] = useState<File | null>(null)
  const [transcribing, setTranscribing] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [resultText, setResultText] = useState<string>("")
  const [error, setError] = useState<string>("")
  const [options, setOptions] = useState({
    model: "whisper-1",
    language: "auto",
    response_format: "json",
    temperature: 0.0,
    batchId: "", // Added batchId state
  })
  const [availableModels, setAvailableModels] = useState<ModelObject[]>([])

  const { toast } = useToast()

  const loadModels = useCallback(async () => {
    try {
      const response = await transcriptionService.getModels()
      setAvailableModels(response.data || [])
      if (response.data && response.data.length > 0 && !options.model) {
        setOptions((prev) => ({ ...prev, model: response.data![0].id }))
      }
    } catch (err) {
      console.error("Failed to load models:", err)
      toast({ title: "Error", description: "Failed to load transcription models.", variant: "destructive" })
    }
  }, [toast, options.model])

  useEffect(() => {
    loadModels()
  }, [loadModels])

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (selectedFile) {
      if (!ALLOWED_FILE_TYPES.includes(selectedFile.type)) {
        setError(`Unsupported file type: ${selectedFile.type}. Please select one of: ${ALLOWED_FILE_TYPES.join(", ")}`)
        setFile(null)
        return
      }
      if (selectedFile.size > MAX_FILE_SIZE) {
        setError(`File size exceeds 25MB limit. Selected file: ${(selectedFile.size / 1024 / 1024).toFixed(2)} MB`)
        setFile(null)
        return
      }
      setFile(selectedFile)
      setError("")
      setResultText("")
    }
  }

  const handleTranscribe = async () => {
    if (!file) {
      setError("请选择一个文件。")
      toast({ title: "Validation Error", description: "Please select a file to transcribe.", variant: "destructive" })
      return
    }
    if (!transcriptionService.getAPIKey()) {
      setError("API Key not set. Please create or select an API Key in Token Management.")
      toast({ title: "Auth Error", description: "API Key is missing.", variant: "destructive" })
      return
    }

    setTranscribing(true)
    setError("")
    setResultText("")
    setUploadProgress(0)

    let currentProgress = 0
    const progressInterval = setInterval(() => {
      currentProgress += 10
      if (currentProgress <= 100) {
        setUploadProgress(currentProgress)
      } else {
        clearInterval(progressInterval)
      }
    }, 200)

    try {
      const response = await transcriptionService.transcribeAudio(file, {
        model: options.model,
        language: options.language,
        response_format: "json",
        temperature: options.temperature,
        batchId: options.batchId.trim() || undefined, // Pass batchId if provided
      })
      clearInterval(progressInterval)
      setUploadProgress(100)

      if (response.data?.text) {
        setResultText(response.data.text) // This is an ack message from mock, actual text is in task list
        toast({ title: "Success", description: "Transcription submitted. Check history for results." })
      } else {
        setError("Transcription submission failed or returned unexpected data.")
        toast({
          title: "Warning",
          description: "Transcription submission failed or returned unexpected data.",
          variant: "default",
        })
      }
    } catch (err) {
      clearInterval(progressInterval)
      setUploadProgress(0)
      const errorMessage = err instanceof Error ? err.message : "转录失败"
      setError(errorMessage)
      toast({ title: "Error", description: errorMessage, variant: "destructive" })
    } finally {
      setTranscribing(false)
    }
  }

  return (
    <Tabs defaultValue="transcribe" className="w-full">
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="transcribe">开始转录</TabsTrigger>
        <TabsTrigger value="history">转录历史</TabsTrigger>
      </TabsList>
      <TabsContent value="transcribe">
        <Card>
          <CardHeader>
            <CardTitle>音频/视频转录</CardTitle>
            <CardDescription>上传您的音频或视频文件以进行转录。</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <Label htmlFor="audioFile" className="mb-2 block">
                选择文件 (最大 25MB)
              </Label>
              <Input
                id="audioFile"
                type="file"
                accept={ALLOWED_FILE_TYPES.join(",")}
                onChange={handleFileChange}
                className="block w-full text-sm file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-primary/10 file:text-primary hover:file:bg-primary/20"
                disabled={transcribing}
              />
              {file && (
                <p className="mt-2 text-sm text-muted-foreground">
                  已选择: {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
                </p>
              )}
            </div>

            {transcribing && uploadProgress > 0 && (
              <div>
                <Label>上传进度</Label>
                <Progress value={uploadProgress} className="w-full" />
                <p className="text-sm text-muted-foreground text-center mt-1">{uploadProgress}%</p>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="model">模型</Label>
                <Select
                  value={options.model}
                  onValueChange={(value) => setOptions({ ...options, model: value })}
                  disabled={transcribing}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择模型" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableModels.map((m) => (
                      <SelectItem key={m.id} value={m.id}>
                        {m.id} ({m.owned_by})
                      </SelectItem>
                    ))}
                    {availableModels.length === 0 && (
                      <SelectItem value="whisper-1" disabled>
                        Whisper-1 (加载中...)
                      </SelectItem>
                    )}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="language">语言</Label>
                <Select
                  value={options.language}
                  onValueChange={(value) => setOptions({ ...options, language: value })}
                  disabled={transcribing}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择语言" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="auto">自动检测</SelectItem>
                    <SelectItem value="zh">中文 (Chinese)</SelectItem>
                    <SelectItem value="en">英文 (English)</SelectItem>
                    {/* Add other languages as needed */}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="batchId">批次号 (可选)</Label>
                <div className="relative">
                  <Package className="absolute left-2.5 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="batchId"
                    type="text"
                    placeholder="例如：PROJECT_A_RUN_1"
                    value={options.batchId}
                    onChange={(e) => setOptions({ ...options, batchId: e.target.value })}
                    className="pl-8"
                    disabled={transcribing}
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="temperature">温度 ({options.temperature.toFixed(1)})</Label>
                <Input
                  id="temperature"
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={options.temperature}
                  onChange={(e) => setOptions({ ...options, temperature: Number.parseFloat(e.target.value) })}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                  disabled={transcribing}
                />
              </div>
            </div>

            {error && (
              <div className="p-3 bg-red-100 dark:bg-red-900/30 border border-red-400 dark:border-red-700 text-red-700 dark:text-red-300 rounded-md">
                <div className="flex items-center">
                  <AlertTriangle className="h-5 w-5 mr-2" />
                  <p className="text-sm">{error}</p>
                </div>
              </div>
            )}

            {resultText && ( // This now shows the submission acknowledgement
              <div className="space-y-2">
                <Label className="text-base font-semibold">提交状态</Label>
                <Textarea
                  value={resultText}
                  readOnly
                  rows={3}
                  className="w-full border-gray-300 rounded-md text-sm bg-gray-50 dark:bg-gray-800/50"
                />
              </div>
            )}
          </CardContent>
          <CardFooter>
            <Button onClick={handleTranscribe} disabled={!file || transcribing} className="w-full">
              {transcribing ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> 转录中...
                </>
              ) : (
                <>
                  <UploadCloud className="mr-2 h-4 w-4" /> 开始转录
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
      </TabsContent>
      <TabsContent value="history">
        {/* The TranscriptionTaskList component is already used here and will have the new features */}
        <TranscriptionTaskList showTitle={true} />
      </TabsContent>
    </Tabs>
  )
}
