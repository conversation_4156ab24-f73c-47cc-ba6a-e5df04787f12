"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import {
  LayoutDashboard,
  KeyRound,
  CalendarCheck2,
  Gift,
  ArrowRightLeft,
  Mic2,
  Layers3,
  ListChecks,
} from "lucide-react"
import type { LucideIcon } from "lucide-react"

interface NavItem {
  href: string
  label: string
  icon: LucideIcon
}

const navItems: NavItem[] = [
  { href: "/dashboard/overview", label: "概览", icon: LayoutDashboard },
  { href: "/dashboard/api-keys", label: "API 密钥", icon: KeyRound },
  { href: "/dashboard/transcription", label: "转录服务", icon: Mic2 },
  { href: "/dashboard/transcription-tasks", label: "转录任务", icon: ListChecks },
  { href: "/dashboard/check-in", label: "签到", icon: CalendarCheck2 },
  { href: "/dashboard/redeem", label: "兑换码", icon: Gift },
  { href: "/dashboard/transfer", label: "转账", icon: ArrowRightLeft },
]

export function Sidebar() {
  const pathname = usePathname()

  return (
    <aside className="fixed left-0 top-0 z-40 flex h-screen w-64 flex-col border-r border-gray-200 bg-gray-50 dark:border-gray-800 dark:bg-gray-900">
      <div className="flex h-16 items-center justify-center border-b border-gray-200 px-6 dark:border-gray-800">
        <Link href="/dashboard/overview" className="flex items-center gap-2">
          <Layers3 className="h-7 w-7 text-green-600" />
          <span className="text-xl font-semibold text-gray-800 dark:text-white">Processing</span>
        </Link>
      </div>
      <nav className="flex-1 space-y-1 overflow-y-auto p-4">
        {navItems.map((item) => (
          <Link
            key={item.label}
            href={item.href}
            className={cn(
              "flex items-center rounded-md px-3 py-2.5 text-sm font-medium text-gray-700 hover:bg-gray-200 dark:text-gray-300 dark:hover:bg-gray-800",
              pathname === item.href ? "bg-gray-200 dark:bg-gray-800 text-green-600 dark:text-green-500" : "",
            )}
          >
            <item.icon
              className={cn("mr-3 h-5 w-5", pathname === item.href ? "text-green-600 dark:text-green-500" : "")}
            />
            {item.label}
          </Link>
        ))}
      </nav>
    </aside>
  )
}
