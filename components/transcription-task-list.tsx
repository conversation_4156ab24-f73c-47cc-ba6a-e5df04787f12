"use client"

import { Label } from "@/components/ui/label"

import type React from "react"
import { useState, useEffect, useCallback, useMemo } from "react"
import { transcriptionService } from "@/lib/transcription-service"
import type { TranscriptionTaskResponse } from "@/lib/api-types"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Textarea } from "@/components/ui/textarea"
import {
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  FileText,
  Search,
  ChevronLeft,
  ChevronRight,
  PackageSearch,
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { cn } from "@/lib/utils"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog"

interface TranscriptionTaskListProps {
  showTitle?: boolean
  itemsPerPage?: number
}

const TaskStatusIcon = ({ status }: { status: string }) => {
  if (status === "completed") return <CheckCircle className="h-4 w-4 text-green-500" />
  if (status === "processing" || status === "pending")
    return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />
  if (status === "failed") return <AlertTriangle className="h-4 w-4 text-red-500" />
  return <FileText className="h-4 w-4 text-gray-500" />
}

export const TranscriptionTaskList: React.FC<TranscriptionTaskListProps> = ({
  showTitle = true,
  itemsPerPage = 10,
}) => {
  const [tasks, setTasks] = useState<TranscriptionTaskResponse[]>([])
  const [loading, setLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalItems, setTotalItems] = useState(0)
  const [searchTerm, setSearchTerm] = useState("")
  const [batchIdSearchTerm, setBatchIdSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all") // 'all', 'completed', 'processing', 'failed'

  const { toast } = useToast()

  const loadTasks = useCallback(async () => {
    setLoading(true)
    try {
      const params: { page: number; per_page: number; status?: string } = {
        page: currentPage,
        per_page: itemsPerPage,
      }
      if (statusFilter !== "all") {
        params.status = statusFilter
      }
      // Note: Batch ID search is client-side in this implementation.
      // If API supports batch ID filtering, it would be added to `params` here.

      const response = await transcriptionService.getTranscriptions(params)
      setTasks(response.items || [])
      setTotalItems(response.total || 0)
      setTotalPages(Math.ceil((response.total || 0) / itemsPerPage))
    } catch (err) {
      console.error("Failed to load transcription tasks:", err)
      toast({ title: "Error", description: "Failed to load transcription tasks.", variant: "destructive" })
      setTasks([])
      setTotalItems(0)
      setTotalPages(1)
    } finally {
      setLoading(false)
    }
  }, [currentPage, itemsPerPage, statusFilter, toast])

  useEffect(() => {
    loadTasks()
  }, [loadTasks])

  // Client-side search for filename and batch ID
  const filteredTasks = useMemo(() => {
    let tempTasks = tasks
    if (searchTerm) {
      tempTasks = tempTasks.filter((task) => task.original_filename.toLowerCase().includes(searchTerm.toLowerCase()))
    }
    if (batchIdSearchTerm) {
      tempTasks = tempTasks.filter(
        (task) =>
          typeof task.metadata?.batchId === "string" &&
          task.metadata.batchId.toLowerCase().includes(batchIdSearchTerm.toLowerCase()),
      )
    }
    return tempTasks
  }, [tasks, searchTerm, batchIdSearchTerm])

  // Polling for task status
  useEffect(() => {
    const processingTasks = tasks.filter((task) => task.status === "processing" || task.status === "pending")
    if (processingTasks.length === 0 || loading) return

    const intervalId = setInterval(async () => {
      let tasksUpdated = false
      const updatedTasks = await Promise.all(
        tasks.map(async (task) => {
          if (task.status === "processing" || task.status === "pending") {
            try {
              const response = await transcriptionService.getTranscriptionStatus(task.id)
              if (response.data && response.data.status !== task.status) {
                tasksUpdated = true
                return response.data
              }
            } catch (err) {
              console.error(`Failed to get status for task ${task.id}`, err)
            }
          }
          return task
        }),
      )
      if (tasksUpdated) {
        setTasks(updatedTasks)
      }
    }, 7000)

    return () => clearInterval(intervalId)
  }, [tasks, loading])

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage)
    }
  }

  const handleFilterChange = () => {
    setCurrentPage(1) // Reset to first page when filters change
    // loadTasks will be called by useEffect due to dependency change
  }

  return (
    <Card>
      {showTitle && (
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <CardTitle>转录任务历史</CardTitle>
              <CardDescription>查看、搜索和管理您的转录任务。</CardDescription>
            </div>
            <Button variant="outline" size="sm" onClick={loadTasks} disabled={loading}>
              <RefreshCw className={`mr-2 h-4 w-4 ${loading ? "animate-spin" : ""}`} />
              刷新
            </Button>
          </div>
        </CardHeader>
      )}
      <CardContent>
        <div className="mb-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 items-end">
          <div className="relative">
            <Label htmlFor="search-filename" className="text-xs">
              按文件名搜索
            </Label>
            <Search className="absolute left-2.5 top-[calc(50%_-_0.5rem_+_7px)] h-4 w-4 text-muted-foreground" />
            <Input
              id="search-filename"
              type="search"
              placeholder="文件名..."
              className="pl-8 w-full"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="relative">
            <Label htmlFor="search-batchid" className="text-xs">
              按批次号搜索
            </Label>
            <PackageSearch className="absolute left-2.5 top-[calc(50%_-_0.5rem_+_7px)] h-4 w-4 text-muted-foreground" />
            <Input
              id="search-batchid"
              type="search"
              placeholder="批次号..."
              className="pl-8 w-full"
              value={batchIdSearchTerm}
              onChange={(e) => setBatchIdSearchTerm(e.target.value)}
            />
          </div>
          <div>
            <Label htmlFor="filter-status" className="text-xs">
              按状态筛选
            </Label>
            <Select
              value={statusFilter}
              onValueChange={(value) => {
                setStatusFilter(value)
                handleFilterChange()
              }}
            >
              <SelectTrigger id="filter-status" className="w-full">
                <SelectValue placeholder="所有状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有状态</SelectItem>
                <SelectItem value="completed">已完成</SelectItem>
                <SelectItem value="processing">处理中</SelectItem>
                <SelectItem value="pending">等待中</SelectItem>
                <SelectItem value="failed">已失败</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {loading && filteredTasks.length === 0 ? (
          <div className="flex justify-center items-center h-64">
            <RefreshCw className="h-8 w-8 animate-spin text-primary" /> <span className="ml-2">加载任务中...</span>
          </div>
        ) : !loading && filteredTasks.length === 0 ? (
          <p className="text-center text-gray-500 dark:text-gray-400 py-16">
            {searchTerm || batchIdSearchTerm || statusFilter !== "all" ? "没有找到匹配的任务。" : "没有转录历史记录。"}
          </p>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="min-w-[150px]">文件名</TableHead>
                  <TableHead className="min-w-[120px]">批次号</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>模型</TableHead>
                  <TableHead>语言</TableHead>
                  <TableHead className="min-w-[170px]">创建时间</TableHead>
                  <TableHead className="text-right min-w-[100px]">时长(秒)</TableHead>
                  <TableHead className="text-right">详情</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTasks.map((task) => (
                  <TableRow key={task.id}>
                    <TableCell className="font-medium truncate max-w-[200px]" title={task.original_filename}>
                      {task.original_filename}
                    </TableCell>
                    <TableCell
                      className="truncate max-w-[150px]"
                      title={typeof task.metadata?.batchId === "string" ? task.metadata.batchId : undefined}
                    >
                      {task.metadata?.batchId || "N/A"}
                    </TableCell>
                    <TableCell>
                      <div
                        className={cn(
                          "flex items-center gap-1.5 px-2 py-0.5 rounded-full text-xs font-medium w-fit",
                          task.status === "completed" &&
                            "bg-green-100 text-green-700 dark:bg-green-900/50 dark:text-green-400",
                          (task.status === "processing" || task.status === "pending") &&
                            "bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-400",
                          task.status === "failed" && "bg-red-100 text-red-700 dark:bg-red-900/50 dark:text-red-400",
                        )}
                      >
                        <TaskStatusIcon status={task.status} />
                        {task.status}
                      </div>
                    </TableCell>
                    <TableCell>{task.model}</TableCell>
                    <TableCell>{task.language || "auto"}</TableCell>
                    <TableCell>{new Date(task.created_time).toLocaleString()}</TableCell>
                    <TableCell className="text-right">{task.duration?.toFixed(1) ?? "N/A"}</TableCell>
                    <TableCell className="text-right">
                      {task.status === "completed" && task.transcription_text && (
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm">
                              查看文本
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="sm:max-w-[625px]">
                            <DialogHeader>
                              <DialogTitle>转录文本: {task.original_filename}</DialogTitle>
                              <DialogDescription>
                                任务ID: {task.id} {task.metadata?.batchId && `| 批次号: ${task.metadata.batchId}`}
                              </DialogDescription>
                            </DialogHeader>
                            <Textarea
                              value={task.transcription_text}
                              readOnly
                              rows={15}
                              className="mt-2 text-xs bg-gray-50 dark:bg-gray-800/50"
                            />
                            <DialogFooter>
                              <DialogClose asChild>
                                <Button type="button">关闭</Button>
                              </DialogClose>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                      )}
                      {task.status === "failed" && task.error_message && (
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="destructive"
                              size="sm"
                              className="bg-red-600/20 text-red-600 hover:bg-red-600/30 border-red-600/30"
                            >
                              查看错误
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="sm:max-w-[625px]">
                            <DialogHeader>
                              <DialogTitle>错误详情: {task.original_filename}</DialogTitle>
                              <DialogDescription>
                                任务ID: {task.id} {task.metadata?.batchId && `| 批次号: ${task.metadata.batchId}`}
                              </DialogDescription>
                            </DialogHeader>
                            <p className="mt-2 text-sm text-red-500 bg-red-50 dark:bg-red-900/20 p-3 rounded-md">
                              {task.error_message}
                            </p>
                            <DialogFooter>
                              <DialogClose asChild>
                                <Button type="button">关闭</Button>
                              </DialogClose>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}

        {!loading && totalItems > 0 && (
          <div className="mt-6 flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              共 {totalItems} 个任务 | 第 {currentPage} 页，共 {totalPages} 页
            </p>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
                上一页
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages || totalPages === 0}
              >
                下一页
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
