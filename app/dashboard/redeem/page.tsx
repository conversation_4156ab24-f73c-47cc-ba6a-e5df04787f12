"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import type React from "react"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Gift } from "lucide-react"
import { useState } from "react"
import { useToast } from "@/hooks/use-toast"

export default function RedeemPage() {
  const [redeemCode, setRedeemCode] = useState("")
  const [loading, setLoading] = useState(false)
  const { toast } = useToast()

  const handleRedeem = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!redeemCode.trim()) {
      toast({ title: "错误", description: "请输入兑换码。", variant: "destructive" })
      return
    }
    setLoading(true)
    // Simulate API call
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000))
      // Mock success/failure
      if (redeemCode.toLowerCase() === "testcode123") {
        toast({
          title: "兑换成功!",
          description: "10000 额度已添加到您的账户 (模拟)。",
        })
        setRedeemCode("")
        // Potentially update user quota in global state/refetch user info
      } else {
        throw new Error("无效或已过期的兑换码。")
      }
    } catch (error) {
      toast({
        title: "兑换失败",
        description: error instanceof Error ? error.message : "无法完成兑换。",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100">兑换码</h1>
      <Card className="max-w-md">
        <CardHeader>
          <CardTitle>使用兑换码增加额度</CardTitle>
          <CardDescription>如果您有兑换码，请在此处输入以获取奖励。</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleRedeem} className="space-y-4">
            <div>
              <label
                htmlFor="redeem-code-input"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >
                兑换码
              </label>
              <Input
                id="redeem-code-input"
                value={redeemCode}
                onChange={(e) => setRedeemCode(e.target.value)}
                placeholder="请输入您的兑换码"
                disabled={loading}
              />
            </div>
            <Button type="submit" className="w-full bg-green-600 hover:bg-green-700 text-white" disabled={loading}>
              <Gift className="mr-2 h-5 w-5" />
              {loading ? "兑换中..." : "立即兑换"}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
