"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Co<PERSON>, <PERSON>Chart2, ShieldCheck, CalendarCheck, Gift, Send } from "lucide-react"
import { authService } from "@/lib/auth-service" // For user data
import type { UserResponse } from "@/lib/api-types"

interface StatCardProps {
  title: string
  value: string | number
  icon: React.ElementType
  color?: string
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon: Icon, color = "text-green-600" }) => (
  <Card className="shadow-sm hover:shadow-md transition-shadow">
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</CardTitle>
      <Icon className={`h-5 w-5 ${color}`} />
    </CardHeader>
    <CardContent>
      <div className={`text-3xl font-bold ${color}`}>{value}</div>
    </CardContent>
  </Card>
)

export default function OverviewPage() {
  const [currentUser, setCurrentUser] = useState<Partial<UserResponse> | null>(null)
  const [isCheckedIn, setIsCheckedIn] = useState(false) // Mock state for check-in

  useEffect(() => {
    const user = authService.getCurrentUser()
    setCurrentUser(user)
    // Mock: check if user has "checked in" today from localStorage or a service
    const lastCheckIn = localStorage.getItem(`lastCheckIn_${user?.id}`)
    if (lastCheckIn) {
      const today = new Date().toDateString()
      if (new Date(lastCheckIn).toDateString() === today) {
        setIsCheckedIn(true)
      }
    }
  }, [])

  const handleCheckIn = () => {
    // Mock check-in logic
    setIsCheckedIn(true)
    if (currentUser?.id) {
      localStorage.setItem(`lastCheckIn_${currentUser.id}`, new Date().toISOString())
    }
    // Here you would typically call an API:
    // await userService.checkIn();
    // And then update quota if successful
    alert("已签到！(mock)")
  }

  const stats = [
    { title: "剩余额度", value: currentUser?.quota ?? 34092, icon: Coins, color: "text-green-600" },
    { title: "总使用次数", value: currentUser?.used_quota ?? 8, icon: BarChart2, color: "text-blue-600" },
    { title: "信任等级", value: 3, icon: ShieldCheck, color: "text-yellow-600" }, // Mocked
    {
      title: "今日状态",
      value: isCheckedIn ? "已签到" : "未签到",
      icon: CalendarCheck,
      color: isCheckedIn ? "text-green-600" : "text-red-600",
    },
  ]

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100 mb-2">概览</h1>
        <p className="text-gray-600 dark:text-gray-400">欢迎来到您的 Processing 控制面板。</p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <StatCard key={stat.title} {...stat} />
        ))}
      </div>

      <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle>快速操作</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              className="w-full bg-green-600 hover:bg-green-700 text-white"
              onClick={handleCheckIn}
              disabled={isCheckedIn}
            >
              <CalendarCheck className="mr-2 h-5 w-5" />
              {isCheckedIn ? "今日已签到" : "每日签到"}
            </Button>
            <div className="space-y-2">
              <Label htmlFor="redeem-code">兑换码</Label>
              <div className="flex gap-2">
                <Input id="redeem-code" placeholder="输入兑换码" />
                <Button variant="outline">
                  <Gift className="mr-2 h-4 w-4" /> 兑换
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="transfer-code">转账</Label>
              <div className="flex gap-2">
                <Input id="transfer-code" placeholder="目标用户ID或邮箱" />
                <Input type="number" placeholder="额度数量" className="w-1/3" />
                <Button variant="outline">
                  <Send className="mr-2 h-4 w-4" /> 转账
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle>API 使用说明</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <p className="text-sm text-gray-700 dark:text-gray-300">使用您的 API Key 来访问我们的转录等服务：</p>
            <div className="rounded-md bg-gray-100 p-3 dark:bg-gray-800">
              <pre className="text-xs text-gray-800 dark:text-gray-200">
                <code>
                  <span className="text-purple-600 dark:text-purple-400">Authorization</span>:{" "}
                  <span className="text-sky-600 dark:text-sky-400">Bearer</span> YOUR_API_KEY
                </code>
              </pre>
            </div>
            <p className="text-sm text-gray-700 dark:text-gray-300">请在 "API 密钥" 页面创建和管理您的 Key。</p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
