"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { CalendarCheck2, Gift } from "lucide-react"
import { useState, useEffect } from "react"
import { authService } from "@/lib/auth-service"
import { useToast } from "@/hooks/use-toast"

export default function CheckInPage() {
  const [isCheckedInToday, setIsCheckedInToday] = useState(false)
  const [loading, setLoading] = useState(true)
  const { toast } = useToast()

  useEffect(() => {
    const user = authService.getCurrentUser()
    if (user?.id) {
      const lastCheckIn = localStorage.getItem(`lastCheckIn_${user.id}`)
      if (lastCheckIn) {
        const today = new Date().toDateString()
        if (new Date(lastCheckIn).toDateString() === today) {
          setIsCheckedInToday(true)
        }
      }
    }
    setLoading(false)
  }, [])

  const handleCheckIn = async () => {
    // This would be an API call in a real app
    // For now, simulate with localStorage and a toast
    setLoading(true)
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 500))
      const user = authService.getCurrentUser()
      if (user?.id) {
        localStorage.setItem(`lastCheckIn_${user.id}`, new Date().toISOString())
        setIsCheckedInToday(true)
        toast({
          title: "签到成功!",
          description: "您已成功签到，额度已增加 (模拟)。",
        })
        // Potentially update user quota in global state/refetch user info
      } else {
        throw new Error("User not found for check-in.")
      }
    } catch (error) {
      toast({
        title: "签到失败",
        description: error instanceof Error ? error.message : "无法完成签到。",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100">每日签到</h1>
      <Card className="max-w-md">
        <CardHeader>
          <CardTitle>获取每日奖励</CardTitle>
          <CardDescription>每天签到可以获取免费额度奖励。</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {loading ? (
            <p>正在检查签到状态...</p>
          ) : isCheckedInToday ? (
            <div className="flex items-center space-x-2 text-green-600 dark:text-green-400">
              <CalendarCheck2 className="h-6 w-6" />
              <p className="text-lg font-semibold">您今天已经签到过了！</p>
            </div>
          ) : (
            <Button
              onClick={handleCheckIn}
              className="w-full bg-green-600 hover:bg-green-700 text-white"
              disabled={loading}
            >
              <Gift className="mr-2 h-5 w-5" />
              {loading ? "处理中..." : "立即签到"}
            </Button>
          )}
          <p className="text-sm text-gray-500 dark:text-gray-400 pt-2">
            签到规则：每日可签到一次，获取随机额度。连续签到可能会有额外奖励哦！ (当前为模拟功能)
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
