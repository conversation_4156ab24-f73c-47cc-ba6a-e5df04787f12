"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { RegisterForm } from "@/components/register-form"
import { authService } from "@/lib/auth-service"
import { Layers3 } from "lucide-react"
import { Toaster } from "@/components/ui/toaster"

export default function RegisterPage() {
  const router = useRouter()

  useEffect(() => {
    // If user is already authenticated, redirect them from register page
    if (authService.isAuthenticated()) {
      router.replace("/dashboard/overview")
    }
  }, [router])

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gray-100 p-4 dark:bg-gray-950">
      <div className="mb-8 flex items-center gap-2">
        <Layers3 className="h-8 w-8 text-green-600" />
        <h1 className="text-3xl font-bold text-gray-800 dark:text-white">Processing</h1>
      </div>
      <RegisterForm />
      <Toaster />
    </div>
  )
}
