"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { authService } from "@/lib/auth-service"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import { Layers3, LogIn, UserPlus, Zap, ShieldCheck, BarChartBig, ArrowRight } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

const FeatureCard = ({
  icon: Icon,
  title,
  description,
}: {
  icon: React.ElementType
  title: string
  description: string
}) => (
  <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm shadow-lg hover:shadow-xl transition-shadow duration-300">
    <CardHeader className="flex flex-row items-center gap-4 pb-2">
      <div className="bg-green-100 dark:bg-green-700/30 p-3 rounded-full">
        <Icon className="h-6 w-6 text-green-600 dark:text-green-400" />
      </div>
      <CardTitle className="text-xl font-semibold text-gray-800 dark:text-white">{title}</CardTitle>
    </CardHeader>
    <CardContent>
      <p className="text-gray-600 dark:text-gray-300">{description}</p>
    </CardContent>
  </Card>
)

export default function RootPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  useEffect(() => {
    const authenticated = authService.isAuthenticated()
    setIsAuthenticated(authenticated)
    setIsLoading(false)
  }, [])

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      router.replace("/dashboard/overview")
    }
  }, [isLoading, isAuthenticated, router])

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center bg-gray-900">
        <Layers3 className="h-16 w-16 text-green-500 animate-spin" />
        <p className="ml-4 text-2xl font-semibold text-white">Loading Processing...</p>
      </div>
    )
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen w-full bg-gradient-to-br from-gray-900 via-gray-800 to-green-900 text-white selection:bg-green-500 selection:text-white">
        {/* Header */}
        <header className="absolute top-0 left-0 right-0 z-10 py-6 px-4 sm:px-8 flex justify-between items-center">
          <Link href="/" className="flex items-center gap-2">
            <Layers3 className="h-8 w-8 text-green-400" />
            <span className="text-2xl font-bold">Processing</span>
          </Link>
          <div className="space-x-3">
            <Button asChild variant="outline" className="bg-transparent text-white border-gray-600 hover:bg-white/10">
              <Link href="/login">
                <LogIn className="mr-2 h-4 w-4" /> Login
              </Link>
            </Button>
            <Button asChild className="bg-green-500 hover:bg-green-600 text-gray-900 font-semibold">
              <Link href="/register">
                <UserPlus className="mr-2 h-4 w-4" /> Register
              </Link>
            </Button>
          </div>
        </header>

        {/* Hero Section */}
        <main className="relative isolate pt-32 pb-16 sm:pt-48 sm:pb-24">
          {/* Background Shapes */}
          <div
            className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80"
            aria-hidden="true"
          >
            <div
              className="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#34d399] to-[#059669] opacity-20 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
              style={{
                clipPath:
                  "polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)",
              }}
            />
          </div>

          <div className="mx-auto max-w-3xl px-6 lg:px-8 text-center">
            <h1 className="text-5xl font-bold tracking-tight sm:text-7xl bg-clip-text text-transparent bg-gradient-to-r from-green-300 via-green-400 to-emerald-500 py-2">
              Unlock Your Audio Data
            </h1>
            <p className="mt-8 text-lg leading-8 text-gray-300 sm:text-xl max-w-2xl mx-auto">
              Powerful, reliable, and scalable transcription services. Manage your API tokens, track usage, and process
              audio effortlessly with Processing.
            </p>
            <div className="mt-12 flex items-center justify-center gap-x-6">
              <Button
                asChild
                size="lg"
                className="bg-green-500 hover:bg-green-600 text-gray-900 font-semibold shadow-lg transform hover:scale-105 transition-transform duration-300"
              >
                <Link href="/register">
                  Get Started Free <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button asChild variant="link" size="lg" className="text-green-400 hover:text-green-300">
                <Link href="#features">
                  Learn More <span aria-hidden="true">→</span>
                </Link>
              </Button>
            </div>
          </div>
        </main>

        {/* Features Section */}
        <section id="features" className="py-16 sm:py-24 bg-gray-800/30 backdrop-blur-md">
          <div className="mx-auto max-w-7xl px-6 lg:px-8">
            <div className="mx-auto max-w-2xl lg:text-center">
              <h2 className="text-base font-semibold leading-7 text-green-400">Everything You Need</h2>
              <p className="mt-2 text-3xl font-bold tracking-tight text-white sm:text-4xl">Why Choose Processing?</p>
              <p className="mt-6 text-lg leading-8 text-gray-300">
                Our platform is designed for developers and businesses who need accurate and fast audio-to-text
                conversion, along with robust API management.
              </p>
            </div>
            <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
              <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
                <FeatureCard
                  icon={Zap}
                  title="High-Speed Transcription"
                  description="Leverage cutting-edge models for fast and accurate audio-to-text conversion. Get results in minutes, not hours."
                />
                <FeatureCard
                  icon={ShieldCheck}
                  title="Secure API Management"
                  description="Easily create, manage, and monitor your API tokens with granular controls and usage tracking."
                />
                <FeatureCard
                  icon={BarChartBig}
                  title="Detailed Usage Analytics"
                  description="Keep track of your transcription quota, task history, and overall usage patterns with our intuitive dashboard."
                />
              </dl>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="py-12 bg-gray-900 border-t border-gray-700/50">
          <div className="mx-auto max-w-7xl px-6 lg:px-8 text-center">
            <Link href="/" className="flex items-center justify-center gap-2 mb-4">
              <Layers3 className="h-7 w-7 text-green-400" />
              <span className="text-xl font-semibold text-white">Processing</span>
            </Link>
            <p className="text-gray-400">&copy; {new Date().getFullYear()} Processing. All rights reserved.</p>
            <div className="mt-4 space-x-4">
              <Link href="#" className="text-sm text-gray-400 hover:text-green-400">
                Privacy Policy
              </Link>
              <Link href="#" className="text-sm text-gray-400 hover:text-green-400">
                Terms of Service
              </Link>
            </div>
          </div>
        </footer>
      </div>
    )
  }

  // Fallback for authenticated users (should be redirected by useEffect)
  return (
    <div className="flex h-screen items-center justify-center bg-gray-900">
      <Layers3 className="h-16 w-16 text-green-500 animate-spin" />
      <p className="ml-4 text-2xl font-semibold text-white">Redirecting to Dashboard...</p>
    </div>
  )
}
