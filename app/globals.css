@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --destructive-bg: #fee2e2;
    --destructive-fg: #991b1b;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    /* Custom status colors */
    --success-bg: #d1fae5; /* Tailwind green-100 */
    --success-fg: #065f46; /* Tailwind green-800 */
    --info-bg: #dbeafe; /* Tailwind blue-100 */
    --info-fg: #1e40af; /* Tailwind blue-800 */

    /* Fo-API Green */
    --fo-green: #10b981; /* Tailwind emerald-500 or green-500 */
    --fo-green-dark: #059669; /* Tailwind emerald-600 or green-600 */
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --destructive-bg: #7f1d1d; /* Tailwind red-900 */
    --destructive-fg: #fecaca; /* Tailwind red-200 */

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    /* Custom status colors */
    --success-bg: #064e3b; /* Tailwind green-900 */
    --success-fg: #a7f3d0; /* Tailwind green-200 */
    --info-bg: #1e3a8a; /* Tailwind blue-900 */
    --info-fg: #bfdbfe; /* Tailwind blue-300 */

    /* Fo-API Green */
    --fo-green: #34d399; /* Tailwind emerald-400 or green-400 */
    --fo-green-dark: #10b981; /* Tailwind emerald-500 or green-500 */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    /* Ensure smooth scrolling for long pages */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}
