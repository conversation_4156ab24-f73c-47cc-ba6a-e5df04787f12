"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { LoginForm } from "@/components/login-form-new" // Assuming this is your primary login form
import { authService } from "@/lib/auth-service"
import { Layers3 } from "lucide-react"
import { Toaster } from "@/components/ui/toaster"
import Link from "next/link"

export default function LoginPage() {
  const router = useRouter()

  useEffect(() => {
    // If user is already authenticated, redirect them from login page
    if (authService.isAuthenticated()) {
      router.replace("/dashboard/overview")
    }
  }, [router])

  const handleLoginSuccess = () => {
    router.replace("/dashboard/overview") // Redirect to dashboard after successful login
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gray-100 p-4 dark:bg-gray-950">
      <div className="mb-8 flex items-center gap-2">
        <Layers3 className="h-8 w-8 text-green-600" />
        <h1 className="text-3xl font-bold text-gray-800 dark:text-white">Processing</h1>
      </div>
      <LoginForm onSuccess={handleLoginSuccess} />
      <p className="mt-6 text-center text-sm text-gray-600 dark:text-gray-400">
        Don&apos;t have an account?{" "}
        <Link href="/register" className="font-medium text-green-600 hover:underline dark:text-green-500">
          Register here
        </Link>
      </p>
      <Toaster />
    </div>
  )
}
