// Based on the provided frontend-integration-guide

export interface ApiResponse<T> {
  data?: T
  message?: string
  timestamp?: string
  detail?: string
  error_code?: string
  // For paginated responses
  items?: T[]
  total?: number
  page?: number
  per_page?: number
  has_next?: boolean
  has_prev?: boolean
}

export class TaskAPIClient {
  private baseURL: string
  private jwtToken?: string
  private apiKey?: string

  constructor(baseURL = "http://localhost:8000") {
    // Default to localhost for dev
    this.baseURL = baseURL
    // Try to load tokens from localStorage
    if (typeof window !== "undefined") {
      this.jwtToken = localStorage.getItem("jwt_token") || undefined
      this.apiKey = localStorage.getItem("api_key") || undefined
    }
  }

  setJWTToken(token: string) {
    this.jwtToken = token
    if (typeof window !== "undefined") {
      localStorage.setItem("jwt_token", token)
    }
  }

  setAPIKey(key: string) {
    this.apiKey = key
    if (typeof window !== "undefined") {
      localStorage.setItem("api_key", key)
    }
  }

  clearAuth() {
    this.jwtToken = undefined
    this.apiKey = undefined
    if (typeof window !== "undefined") {
      localStorage.removeItem("jwt_token")
      localStorage.removeItem("api_key")
      localStorage.removeItem("user_info")
    }
  }

  getJWTToken(): string | undefined {
    return this.jwtToken
  }

  getAPIKey(): string | undefined {
    return this.apiKey
  }

  private getHeaders(useAPIKey = false, isFormData = false): Record<string, string> {
    const headers: Record<string, string> = {}

    if (!isFormData) {
      headers["Content-Type"] = "application/json"
    }

    if (useAPIKey && this.apiKey) {
      headers["Authorization"] = `Bearer ${this.apiKey}`
    } else if (!useAPIKey && this.jwtToken) {
      headers["Authorization"] = `Bearer ${this.jwtToken}`
    }
    return headers
  }

  async request<T>(endpoint: string, options: RequestInit = {}, useAPIKey = false): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`
    const isFormData = options.body instanceof FormData
    const headers = this.getHeaders(useAPIKey, isFormData)

    // Ensure options.headers doesn't overwrite critical parts of our headers
    const finalHeaders = { ...headers, ...options.headers }

    // For FormData, fetch handles Content-Type automatically, so remove it if we set it
    if (isFormData) {
      delete finalHeaders["Content-Type"]
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers: finalHeaders,
      })

      if (!response.ok) {
        const errorData = await response.json()
        console.error("API Error:", errorData)
        throw new Error(errorData.detail || `Request failed with status ${response.status}`)
      }
      // Handle cases where response might be empty (e.g., DELETE success)
      const contentType = response.headers.get("content-type")
      if (contentType && contentType.includes("application/json")) {
        return response.json() as Promise<ApiResponse<T>>
      }
      return { message: "Operation successful" } as unknown as Promise<ApiResponse<T>>
    } catch (error) {
      console.error(`Fetch error for ${options.method || "GET"} ${url}:`, error)
      if (error instanceof TypeError && error.message.toLowerCase().includes("failed to fetch")) {
        throw new Error(
          `Network error: Failed to fetch API endpoint at ${url}. ` +
            `Please ensure the API server is running and accessible, and that CORS is configured correctly on the server. ` +
            `If you intend to use mock data, ensure the NEXT_PUBLIC_USE_MOCKS environment variable is set to "true" (currently it defaults to using mocks unless NEXT_PUBLIC_USE_MOCKS is "false").`,
        )
      }
      if (error instanceof Error) {
        // Re-throw other types of errors
        throw new Error(`API request failed: ${error.message}`)
      }
      // Fallback for non-Error objects thrown
      throw new Error("An unknown error occurred during the API request.")
    }
  }
}

// Global API client instance
export const apiClient = new TaskAPIClient(process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000")
