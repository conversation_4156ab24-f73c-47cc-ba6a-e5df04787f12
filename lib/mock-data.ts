/**
 * Mock数据集中管理
 * 包含所有服务的Mock数据，便于统一管理和维护
 */

import type {
  AuthTokenResponse,
  UserResponse as AuthUserResponse,
  TokenResponse,
  ModelsResponse,
  TranscriptionResponse,
  TranscriptionTaskResponse,
  ModelObject,
} from "./api-types"

// =============================================================================
// 认证服务 Mock 数据
// =============================================================================

/**
 * Mock用户登录成功响应 - 普通用户
 */
export const MOCK_USER_LOGIN_SUCCESS: AuthTokenResponse = {
  access_token: "mock_jwt_token_12345",
  token_type: "bearer",
  expires_in: 3600,
  user_id: 1,
  username: "testuser",
  role: 1, // 1 for normal user, 100 for admin
}

/**
 * Mock管理员登录成功响应
 */
export const MOCK_ADMIN_LOGIN_SUCCESS: AuthTokenResponse = {
  access_token: "mock_admin_jwt_token_67890",
  token_type: "bearer",
  expires_in: 3600,
  user_id: 99, // Different user_id for admin
  username: "adminuser",
  role: 100, // Admin role
}

/**
 * Mock用户注册成功响应
 */
export const MOCK_USER_REGISTER_SUCCESS = {
  message: "Registration successful",
  user_id: 2,
  verification_required: false,
}

/**
 * Mock当前用户信息
 */
export const MOCK_CURRENT_USER: AuthUserResponse = {
  id: 1,
  username: "testuser",
  display_name: "Test User",
  email: "<EMAIL>",
  role: 1,
  status: 1,
  group: "default",
  quota: 10000,
  used_quota: 500,
  request_count: 120,
  created_time: new Date().toISOString(),
  last_login_time: new Date().toISOString(),
  aff_code: "AFF123",
  aff_quota: 500,
}

// =============================================================================
// 转录服务 Mock 数据
// =============================================================================

/**
 * Mock可用模型列表
 */
export const MOCK_MODELS: ModelsResponse = {
  object: "list",
  data: [
    {
      id: "whisper-1",
      object: "model",
      created: Date.now() / 1000,
      owned_by: "openai",
      permission: [],
      root: "whisper-1",
      parent: null,
    },
    {
      id: "whisper-large-v2",
      object: "model",
      created: Date.now() / 1000,
      owned_by: "community",
      permission: [],
      root: "whisper-large-v2",
      parent: null,
    },
  ],
}

/**
 * Mock转录结果
 */
export const MOCK_TRANSCRIPTION_RESULT: TranscriptionResponse = {
  text: "This is a mock transcription of the uploaded audio file.",
  task: "transcribe",
  language: "en",
  duration: 15.5,
  segments: [
    {
      id: 0,
      seek: 0,
      start: 0.0,
      end: 5.0,
      text: "This is a mock transcription",
      tokens: [],
      temperature: 0.0,
      avg_logprob: -0.5,
      compression_ratio: 1.5,
      no_speech_prob: 0.1,
    },
  ],
}

/**
 * 生成Mock转录任务列表
 * @param count 任务数量
 * @returns TranscriptionTaskResponse[]
 */
export function generateMockTranscriptionTasks(count: number = 25): TranscriptionTaskResponse[] {
  return Array.from({ length: count }, (_, i) => ({
    id: `task_mock_${i + 1}`,
    status: i % 4 === 0 ? "completed" : i % 4 === 1 ? "processing" : i % 4 === 2 ? "pending" : "failed",
    original_filename: `audio_example_${i + 1}.${i % 2 === 0 ? "mp3" : "wav"}`,
    file_size: 1024 * 1024 * ((i % 5) + 1), // 1-5MB
    duration: (i + 1) * 30.5, // seconds
    model: i % 2 === 0 ? "whisper-1" : "whisper-large-v2",
    language: i % 3 === 0 ? "en" : i % 3 === 1 ? "zh" : "auto",
    response_format: "json",
    transcription_text: i % 4 === 0 ? `This is mock transcription for audio_example_${i + 1}.` : null,
    confidence_score: i % 4 === 0 ? Math.random() * 0.3 + 0.7 : null,
    quota_consumed: (i + 1) * 30,
    error_message: i % 4 === 3 ? "Mock processing error: timeout" : null,
    created_time: new Date(Date.now() - 86400000 * (i + 1)).toISOString(),
    started_at: i % 4 !== 2 ? new Date(Date.now() - 86400000 * (i + 1) + 10000).toISOString() : null,
    completed_at: i % 4 === 0 ? new Date(Date.now() - 86400000 * (i + 1) + (i + 1) * 30 * 1000).toISOString() : null,
    processing_time: i % 4 === 0 ? (i + 1) * 30.0 : null,
    metadata: {
      index: i,
      // Add batchId for some tasks
      batchId:
        i % 5 === 0 ? `BATCH_XYZ_${Math.floor(i / 5)}` : i % 5 === 2 ? `BATCH_ABC_${Math.floor(i / 5)}` : undefined,
    },
  }))
}

// =============================================================================
// Token管理服务 Mock 数据
// =============================================================================

/**
 * Mock Token ID 计数器
 */
export let nextMockTokenId = 3

/**
 * 获取下一个Mock Token ID
 */
export function getNextMockTokenId(): number {
  return nextMockTokenId++
}

/**
 * Mock Token列表
 */
export const MOCK_TOKENS: TokenResponse[] = [
  {
    id: 1,
    user_id: 1,
    key: "sk-mocktoken123abc",
    name: "My First Mock Token",
    status: 1,
    status_text: "Active",
    group: "default",
    remain_quota: 10000,
    used_quota: 500,
    unlimited_quota: false,
    model_limits_enabled: false,
    model_limits: [],
    allow_ips: [],
    expired_time: -1,
    request_count: 120,
    last_used_time: new Date(Date.now() - 86400000).toISOString(), // Yesterday
    created_time: new Date(Date.now() - 86400000 * 7).toISOString(), // Last week
  },
  {
    id: 2,
    user_id: 1,
    key: "sk-mocktoken789xyz",
    name: "Unlimited Project Token",
    status: 1,
    status_text: "Active",
    group: "project-x",
    remain_quota: 0,
    used_quota: 15000,
    unlimited_quota: true,
    model_limits_enabled: true,
    model_limits: ["whisper-1", "gpt-4"],
    allow_ips: ["*************"],
    expired_time: new Date(Date.now() + 86400000 * 30).toISOString(), // Expires in 30 days
    request_count: 500,
    last_used_time: new Date().toISOString(),
    created_time: new Date(Date.now() - 86400000 * 2).toISOString(), // Two days ago
  },
]

// =============================================================================
// Mock 数据状态管理
// =============================================================================

/**
 * Mock数据状态管理类
 * 用于管理动态Mock数据的状态变化
 */
export class MockDataManager {
  private static transcriptionTasks: TranscriptionTaskResponse[] = generateMockTranscriptionTasks()
  private static tokens: TokenResponse[] = [...MOCK_TOKENS]

  /**
   * 获取转录任务列表
   */
  static getTranscriptionTasks(): TranscriptionTaskResponse[] {
    return [...this.transcriptionTasks]
  }

  /**
   * 添加转录任务
   */
  static addTranscriptionTask(task: TranscriptionTaskResponse): void {
    this.transcriptionTasks.unshift(task)
  }

  /**
   * 更新转录任务
   */
  static updateTranscriptionTask(taskId: string, updates: Partial<TranscriptionTaskResponse>): boolean {
    const index = this.transcriptionTasks.findIndex(t => t.id === taskId)
    if (index > -1) {
      this.transcriptionTasks[index] = { ...this.transcriptionTasks[index], ...updates }
      return true
    }
    return false
  }

  /**
   * 获取Token列表
   */
  static getTokens(): TokenResponse[] {
    return [...this.tokens]
  }

  /**
   * 添加Token
   */
  static addToken(token: TokenResponse): void {
    this.tokens.push(token)
  }

  /**
   * 删除Token
   */
  static deleteToken(tokenId: number): boolean {
    const index = this.tokens.findIndex(t => t.id === tokenId)
    if (index > -1) {
      this.tokens.splice(index, 1)
      return true
    }
    return false
  }

  /**
   * 更新Token
   */
  static updateToken(tokenId: number, updates: Partial<TokenResponse>): boolean {
    const index = this.tokens.findIndex(t => t.id === tokenId)
    if (index > -1) {
      this.tokens[index] = { ...this.tokens[index], ...updates }
      return true
    }
    return false
  }
}
