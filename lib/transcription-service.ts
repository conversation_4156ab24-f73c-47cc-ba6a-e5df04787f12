import { apiClient, type Task<PERSON>IClient, type ApiR<PERSON>ponse } from "./api-client"
import type {
  ModelsResponse,
  TranscriptionResponse as ApiTranscriptionResponse,
  TranscriptionTaskResponse,
  TranscriptionTaskListResponse as ApiTranscriptionTaskListResponse,
} from "./api-types"
import { shouldUseMocks, mockLog, createMockDelay, MOCK_DELAYS } from "./mock-config"
import {
  MOCK_MODELS,
  MOCK_TRANSCRIPTION_RESULT,
  MockDataManager,
} from "./mock-data"

export class TranscriptionService {
  private client: TaskAPIClient
  private useMocks: boolean

  constructor(client: TaskAPIClient, useMocks?: boolean) {
    this.client = client
    // 使用统一的Mock配置判断逻辑
    this.useMocks = useMocks !== undefined ? useMocks : shouldUseMocks()
  }

  async getModels(): Promise<ApiResponse<ModelsResponse["data"]>> {
    if (this.useMocks) {
      mockLog("TranscriptionService", "getModels")
      await createMockDelay(MOCK_DELAYS.FAST)
      return Promise.resolve({ data: MOCK_MODELS.data })
    }
    const response = await this.client.request<ModelsResponse>("/v1/models", {}, true)
    return { data: response.data?.data, message: response.message }
  }

  async transcribeAudio(
    file: File,
    options: {
      model?: string
      language?: string
      prompt?: string
      response_format?: string
      temperature?: number
      batchId?: string // Allow passing batchId
    } = {},
  ): Promise<ApiResponse<ApiTranscriptionResponse>> {
    if (this.useMocks) {
      mockLog("TranscriptionService", "transcribeAudio", { filename: file.name, options })
      await createMockDelay(MOCK_DELAYS.SLOW)

      const newTaskId = `task_mock_transcribe_${Date.now()}`
      const newTask: TranscriptionTaskResponse = {
        id: newTaskId,
        status: "pending", // Start as pending
        original_filename: file.name,
        file_size: file.size,
        duration: null,
        model: options.model || "whisper-1",
        language: options.language || "auto",
        response_format: options.response_format || "json",
        transcription_text: null,
        confidence_score: null,
        quota_consumed: 0,
        error_message: null,
        created_time: new Date().toISOString(),
        started_at: null,
        completed_at: null,
        processing_time: null,
        metadata: { batchId: options.batchId },
      }

      MockDataManager.addTranscriptionTask(newTask)

      // Simulate processing
      setTimeout(() => {
        MockDataManager.updateTranscriptionTask(newTaskId, {
          status: "processing",
          started_at: new Date().toISOString()
        })
      }, 2000)

      setTimeout(() => {
        const duration = Math.random() * 300
        MockDataManager.updateTranscriptionTask(newTaskId, {
          status: "completed",
          transcription_text: `Mocked transcription for ${file.name}: ${MOCK_TRANSCRIPTION_RESULT.text}`,
          duration: duration,
          confidence_score: Math.random() * 0.3 + 0.7,
          quota_consumed: Math.floor(duration * 0.5),
          completed_at: new Date().toISOString(),
          processing_time: 6.0 // Approximate processing time
        })
      }, 8000)

      return Promise.resolve({
        data: {
          ...MOCK_TRANSCRIPTION_RESULT,
          text: `Transcription for ${file.name} submitted. Check task list for status.`, // Initial response is just ack
        },
      })
    }

    const formData = new FormData()
    formData.append("file", file)
    formData.append("model", options.model || "whisper-1")
    if (options.language) formData.append("language", options.language)
    if (options.prompt) formData.append("prompt", options.prompt)
    if (options.response_format) formData.append("response_format", options.response_format)
    if (options.temperature !== undefined) formData.append("temperature", options.temperature.toString())
    // Add batchId to metadata if provided
    if (options.batchId) {
      formData.append("metadata", JSON.stringify({ batchId: options.batchId }))
    }

    return this.client.request<ApiTranscriptionResponse>(
      "/v1/audio/transcriptions",
      { method: "POST", body: formData },
      true,
    )
  }

  async getTranscriptionStatus(taskId: string): Promise<ApiResponse<TranscriptionTaskResponse>> {
    if (this.useMocks) {
      mockLog("TranscriptionService", "getTranscriptionStatus", { taskId })
      await createMockDelay(MOCK_DELAYS.FAST)

      const tasks = MockDataManager.getTranscriptionTasks()
      const task = tasks.find((t) => t.id === taskId)

      if (task) {
        // Simulate status change for processing tasks if not already completed/failed by transcribeAudio mock
        if (task.status === "pending" && Math.random() < 0.5) {
          MockDataManager.updateTranscriptionTask(taskId, {
            status: "processing",
            started_at: new Date().toISOString()
          })
        } else if (task.status === "processing" && Math.random() < 0.3) {
          const duration = task.duration || Math.random() * 300
          MockDataManager.updateTranscriptionTask(taskId, {
            status: "completed",
            transcription_text: `Updated mock transcription for ${task.original_filename}.`,
            duration: duration,
            confidence_score: task.confidence_score || Math.random() * 0.3 + 0.7,
            quota_consumed: task.quota_consumed || Math.floor(duration * 0.5),
            completed_at: new Date().toISOString(),
            processing_time: task.started_at
              ? (new Date().getTime() - new Date(task.started_at).getTime()) / 1000
              : Math.random() * 10 + 5
          })
        }

        // Get updated task
        const updatedTasks = MockDataManager.getTranscriptionTasks()
        const updatedTask = updatedTasks.find((t) => t.id === taskId)
        return Promise.resolve({ data: updatedTask! })
      }
      return Promise.reject(new Error("Mock task not found"))
    }
    return this.client.request<TranscriptionTaskResponse>(`/v1/transcriptions/${taskId}`, {}, true)
  }

  async getTranscriptions(
    params: {
      page?: number
      per_page?: number
      status?: string
      // Add batchId to params if API supports it, otherwise it's client-side
      // batchId?: string
    } = {},
  ): Promise<ApiTranscriptionTaskListResponse> {
    // Ensure return type matches API spec
    if (this.useMocks) {
      mockLog("TranscriptionService", "getTranscriptions", params)
      await createMockDelay(MOCK_DELAYS.NORMAL)

      let filteredTasks = [...MockDataManager.getTranscriptionTasks()].sort(
        (a, b) => new Date(b.created_time).getTime() - new Date(a.created_time).getTime(),
      )

      if (params.status && params.status !== "all") {
        filteredTasks = filteredTasks.filter((t) => t.status === params.status)
      }
      // Client-side filtering for batchId for mock
      // if (params.batchId) {
      //   filteredTasks = filteredTasks.filter(t => t.metadata?.batchId === params.batchId);
      // }

      const page = params.page || 1
      const perPage = params.per_page || 10
      const total = filteredTasks.length
      const start = (page - 1) * perPage
      const end = start + perPage
      const paginatedTasks = filteredTasks.slice(start, end)

      return Promise.resolve({
        items: paginatedTasks,
        total: total,
        page: page,
        per_page: perPage,
        has_next: end < total,
        has_prev: start > 0,
      })
    }
    const queryStringParams = new URLSearchParams()
    if (params.page) queryStringParams.append("page", String(params.page))
    if (params.per_page) queryStringParams.append("per_page", String(params.per_page))
    if (params.status && params.status !== "all") queryStringParams.append("status", params.status)
    // if (params.batchId) queryStringParams.append("metadata_batchId", params.batchId); // Example if API supports metadata query

    const queryString = queryStringParams.toString()
    const response = await this.client.request<ApiTranscriptionTaskListResponse>(`/v1/transcriptions?${queryString}`, {}, true)
    return response.data || { items: [], total: 0, page: 1, per_page: 10, has_next: false, has_prev: false }
  }
}

export const transcriptionService = new TranscriptionService(apiClient)
