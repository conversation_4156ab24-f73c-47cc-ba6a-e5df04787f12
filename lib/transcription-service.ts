import { apiClient, type TaskAPIClient, type ApiResponse } from "./api-client"
import type {
  ModelsResponse,
  TranscriptionResponse as ApiTranscriptionResponse,
  TranscriptionTaskResponse,
  TranscriptionTaskListResponse as ApiTranscriptionTaskListResponse, // Import the list response type
} from "./api-types"

// Mock data for transcription
const MOCK_MODELS: ModelsResponse = {
  object: "list",
  data: [
    {
      id: "whisper-1",
      object: "model",
      created: Date.now() / 1000,
      owned_by: "openai",
      permission: [],
      root: "whisper-1",
      parent: null,
    },
    {
      id: "whisper-large-v2",
      object: "model",
      created: Date.now() / 1000,
      owned_by: "community",
      permission: [],
      root: "whisper-large-v2",
      parent: null,
    },
  ],
}

const MOCK_TRANSCRIPTION_RESULT: ApiTranscriptionResponse = {
  text: "This is a mock transcription of the uploaded audio file.",
  task: "transcribe",
  language: "en",
  duration: 15.5,
  segments: [
    {
      id: 0,
      seek: 0,
      start: 0.0,
      end: 5.0,
      text: "This is a mock transcription",
      tokens: [],
      temperature: 0.0,
      avg_logprob: -0.5,
      compression_ratio: 1.5,
      no_speech_prob: 0.1,
    },
  ],
}

const mockTranscriptionTasks: TranscriptionTaskResponse[] = Array.from({ length: 25 }, (_, i) => ({
  id: `task_mock_${i + 1}`,
  status: i % 4 === 0 ? "completed" : i % 4 === 1 ? "processing" : i % 4 === 2 ? "pending" : "failed",
  original_filename: `audio_example_${i + 1}.${i % 2 === 0 ? "mp3" : "wav"}`,
  file_size: 1024 * 1024 * ((i % 5) + 1), // 1-5MB
  duration: (i + 1) * 30.5, // seconds
  model: i % 2 === 0 ? "whisper-1" : "whisper-large-v2",
  language: i % 3 === 0 ? "en" : i % 3 === 1 ? "zh" : "auto",
  response_format: "json",
  transcription_text: i % 4 === 0 ? `This is mock transcription for audio_example_${i + 1}.` : null,
  confidence_score: i % 4 === 0 ? Math.random() * 0.3 + 0.7 : null,
  quota_consumed: (i + 1) * 30,
  error_message: i % 4 === 3 ? "Mock processing error: timeout" : null,
  created_time: new Date(Date.now() - 86400000 * (i + 1)).toISOString(),
  started_at: i % 4 !== 2 ? new Date(Date.now() - 86400000 * (i + 1) + 10000).toISOString() : null,
  completed_at: i % 4 === 0 ? new Date(Date.now() - 86400000 * (i + 1) + (i + 1) * 30 * 1000).toISOString() : null,
  processing_time: i % 4 === 0 ? (i + 1) * 30.0 : null,
  metadata: {
    index: i,
    // Add batchId for some tasks
    batchId:
      i % 5 === 0 ? `BATCH_XYZ_${Math.floor(i / 5)}` : i % 5 === 2 ? `BATCH_ABC_${Math.floor(i / 5)}` : undefined,
  },
}))

export class TranscriptionService {
  private client: TaskAPIClient
  private useMocks: boolean

  constructor(client: TaskAPIClient, useMocks = true) {
    this.client = client
    this.useMocks = useMocks
  }

  async getModels(): Promise<ApiResponse<ModelsResponse["data"]>> {
    if (this.useMocks) {
      return Promise.resolve({ data: MOCK_MODELS.data })
    }
    const response = await this.client.request<ModelsResponse>("/v1/models", {}, true)
    return { data: response.data, message: response.message }
  }

  async transcribeAudio(
    file: File,
    options: {
      model?: string
      language?: string
      prompt?: string
      response_format?: string
      temperature?: number
      batchId?: string // Allow passing batchId
    } = {},
  ): Promise<ApiResponse<ApiTranscriptionResponse>> {
    if (this.useMocks) {
      await new Promise((resolve) => setTimeout(resolve, 1500))
      const newTaskId = `task_mock_transcribe_${Date.now()}`
      const newTask: TranscriptionTaskResponse = {
        id: newTaskId,
        status: "pending", // Start as pending
        original_filename: file.name,
        file_size: file.size,
        duration: null,
        model: options.model || "whisper-1",
        language: options.language || "auto",
        response_format: options.response_format || "json",
        transcription_text: null,
        confidence_score: null,
        quota_consumed: 0,
        error_message: null,
        created_time: new Date().toISOString(),
        started_at: null,
        completed_at: null,
        processing_time: null,
        metadata: { batchId: options.batchId },
      }
      mockTranscriptionTasks.unshift(newTask) // Add to the beginning
      // Simulate processing
      setTimeout(() => {
        const idx = mockTranscriptionTasks.findIndex((t) => t.id === newTaskId)
        if (idx > -1) {
          mockTranscriptionTasks[idx].status = "processing"
          mockTranscriptionTasks[idx].started_at = new Date().toISOString()
        }
      }, 2000)
      setTimeout(() => {
        const idx = mockTranscriptionTasks.findIndex((t) => t.id === newTaskId)
        if (idx > -1) {
          mockTranscriptionTasks[idx].status = "completed"
          mockTranscriptionTasks[idx].transcription_text =
            `Mocked transcription for ${file.name}: ${MOCK_TRANSCRIPTION_RESULT.text}`
          mockTranscriptionTasks[idx].duration = Math.random() * 300
          mockTranscriptionTasks[idx].confidence_score = Math.random() * 0.3 + 0.7
          mockTranscriptionTasks[idx].quota_consumed = Math.floor(mockTranscriptionTasks[idx].duration! * 0.5)
          mockTranscriptionTasks[idx].completed_at = new Date().toISOString()
          mockTranscriptionTasks[idx].processing_time =
            (new Date(mockTranscriptionTasks[idx].completed_at!).getTime() -
              new Date(mockTranscriptionTasks[idx].started_at!).getTime()) /
            1000
        }
      }, 8000)

      return Promise.resolve({
        data: {
          ...MOCK_TRANSCRIPTION_RESULT,
          text: `Transcription for ${file.name} submitted. Check task list for status.`, // Initial response is just ack
        },
      })
    }

    const formData = new FormData()
    formData.append("file", file)
    formData.append("model", options.model || "whisper-1")
    if (options.language) formData.append("language", options.language)
    if (options.prompt) formData.append("prompt", options.prompt)
    if (options.response_format) formData.append("response_format", options.response_format)
    if (options.temperature !== undefined) formData.append("temperature", options.temperature.toString())
    // Add batchId to metadata if provided
    if (options.batchId) {
      formData.append("metadata", JSON.stringify({ batchId: options.batchId }))
    }

    return this.client.request<ApiTranscriptionResponse>(
      "/v1/audio/transcriptions",
      { method: "POST", body: formData },
      true,
    )
  }

  async getTranscriptionStatus(taskId: string): Promise<ApiResponse<TranscriptionTaskResponse>> {
    if (this.useMocks) {
      const task = mockTranscriptionTasks.find((t) => t.id === taskId)
      if (task) {
        // Simulate status change for processing tasks if not already completed/failed by transcribeAudio mock
        if (task.status === "pending" && Math.random() < 0.5) {
          task.status = "processing"
          task.started_at = new Date().toISOString()
        } else if (task.status === "processing" && Math.random() < 0.3) {
          task.status = "completed"
          task.transcription_text = `Updated mock transcription for ${task.original_filename}.`
          task.duration = task.duration || Math.random() * 300
          task.confidence_score = task.confidence_score || Math.random() * 0.3 + 0.7
          task.quota_consumed = task.quota_consumed || Math.floor(task.duration! * 0.5)
          task.completed_at = new Date().toISOString()
          task.processing_time = task.started_at
            ? (new Date(task.completed_at!).getTime() - new Date(task.started_at!).getTime()) / 1000
            : Math.random() * 10 + 5
        }
        return Promise.resolve({ data: task })
      }
      return Promise.reject(new Error("Mock task not found"))
    }
    return this.client.request<TranscriptionTaskResponse>(`/v1/transcriptions/${taskId}`, {}, true)
  }

  async getTranscriptions(
    params: {
      page?: number
      per_page?: number
      status?: string
      // Add batchId to params if API supports it, otherwise it's client-side
      // batchId?: string
    } = {},
  ): Promise<ApiTranscriptionTaskListResponse> {
    // Ensure return type matches API spec
    if (this.useMocks) {
      let filteredTasks = [...mockTranscriptionTasks].sort(
        (a, b) => new Date(b.created_time).getTime() - new Date(a.created_time).getTime(),
      )

      if (params.status && params.status !== "all") {
        filteredTasks = filteredTasks.filter((t) => t.status === params.status)
      }
      // Client-side filtering for batchId for mock
      // if (params.batchId) {
      //   filteredTasks = filteredTasks.filter(t => t.metadata?.batchId === params.batchId);
      // }

      const page = params.page || 1
      const perPage = params.per_page || 10
      const total = filteredTasks.length
      const start = (page - 1) * perPage
      const end = start + perPage
      const paginatedTasks = filteredTasks.slice(start, end)

      return Promise.resolve({
        items: paginatedTasks,
        total: total,
        page: page,
        per_page: perPage,
        has_next: end < total,
        has_prev: start > 0,
      })
    }
    const queryStringParams = new URLSearchParams()
    if (params.page) queryStringParams.append("page", String(params.page))
    if (params.per_page) queryStringParams.append("per_page", String(params.per_page))
    if (params.status && params.status !== "all") queryStringParams.append("status", params.status)
    // if (params.batchId) queryStringParams.append("metadata_batchId", params.batchId); // Example if API supports metadata query

    const queryString = queryStringParams.toString()
    return this.client.request<ApiTranscriptionTaskListResponse>(`/v1/transcriptions?${queryString}`, {}, true)
  }
}

export const transcriptionService = new TranscriptionService(apiClient, process.env.NEXT_PUBLIC_USE_MOCKS !== "false")
