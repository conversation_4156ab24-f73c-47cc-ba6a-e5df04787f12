// Based on OpenAPI schema and frontend guide

// Auth
export interface UserRegisterRequest {
  username: string
  password: string
  email?: string
  display_name?: string
  invitation_code?: string
}

export interface UserLoginRequest {
  username: string
  password: string
}

export interface AuthTokenResponse {
  // This is for JWT auth token
  access_token: string
  token_type: string // "bearer"
  expires_in: number
  user_id: number
  username: string
  role: number
}

export interface UserResponse {
  // For /auth/me and user listings
  id: number
  username: string
  display_name: string | null
  email: string | null
  role: number
  status: number
  group: string
  quota: number
  used_quota: number
  request_count: number
  created_time: string // ISO Date string
  last_login_time: string | null // ISO Date string
  aff_code?: string | null // from UserResponse in users module
  aff_quota?: number // from UserResponse in users module
}

// API Tokens (the ones users create for API access)
export interface TokenCreateRequest {
  name: string
  remain_quota?: number
  unlimited_quota?: boolean
  model_limits_enabled?: boolean
  model_limits?: string[]
  allow_ips?: string[]
  expired_time?: number // Unix timestamp or -1 for no expiry
  group?: string
}

export interface TokenResponse {
  // This is for API Key type token
  id: number
  user_id: number
  key: string // The actual API key, e.g., sk-xxxx
  name: string
  status: number // e.g., 1 for active
  status_text: string
  group: string | null
  remain_quota: number
  used_quota: number
  unlimited_quota: boolean
  model_limits_enabled: boolean
  model_limits: string[]
  allow_ips: string[]
  expired_time: number | string // Can be -1 or ISO string from mock
  request_count: number
  last_used_time: string | null // ISO Date string
  created_time: string // ISO Date string
}

export interface TokenUpdateRequest {
  name?: string
  status?: number
  model_limits_enabled?: boolean
  model_limits?: string[]
  allow_ips?: string[]
  expired_time?: number
  group?: string
}

export interface TokenListResponse {
  items: TokenResponse[] // Changed from 'tokens' to 'items' to match typical paginated API responses
  total: number
  page: number
  per_page: number
  has_next: boolean
  has_prev: boolean
}

// Transcription
export interface ModelObject {
  id: string
  object: string // "model"
  created: number // Unix timestamp
  owned_by: string
  permission: any[]
  root: string
  parent: string | null
}

export interface ModelsResponse {
  object: string // "list"
  data: ModelObject[]
}

export interface TranscriptionResponse {
  text: string
  task?: string // "transcribe"
  language?: string | null
  duration?: number | null
  segments?: any[] | null // Array of segment objects
}

export interface TranscriptionTaskResponse {
  id: string
  status: string // "pending", "processing", "completed", "failed"
  original_filename: string
  file_size: number // bytes
  duration: number | null // seconds
  model: string
  language: string | null
  response_format: string
  transcription_text: string | null
  confidence_score: number | null
  quota_consumed: number
  error_message: string | null
  created_time: string // ISO Date string
  started_at: string | null // ISO Date string
  completed_at: string | null // ISO Date string
  processing_time: number | null // seconds
  metadata: Record<string, any>
}

export interface TranscriptionTaskListResponse {
  items: TranscriptionTaskResponse[] // Changed from 'tasks'
  total: number
  page: number
  per_page: number
  has_next: boolean
  has_prev: boolean
}
