import { apiClient, type TaskAPIClient, type ApiResponse } from "./api-client"
import type {
  TokenCreateRequest,
  TokenResponse,
  TokenListResponse as ApiTokenListResponse,
  TokenUpdateRequest,
} from "./api-types"
import { shouldUseMocks, mockLog, createMockDelay, MOCK_DELAYS } from "./mock-config"
import { MockDataManager, getNextMockTokenId } from "./mock-data"

export interface TokenListResponse extends ApiTokenListResponse {
  // The API spec uses 'tokens', but the guide uses 'tokens'. Let's align with the guide's component.
  // The actual API response has 'items' for the list. We'll map it.
}

export class TokenService {
  private client: TaskAPIClient
  private useMocks: boolean

  constructor(client: TaskAPIClient, useMocks?: boolean) {
    this.client = client
    // 使用统一的Mock配置判断逻辑
    this.useMocks = useMocks !== undefined ? useMocks : shouldUseMocks()
  }

  async createToken(tokenData: TokenCreateRequest): Promise<ApiResponse<TokenResponse>> {
    if (this.useMocks) {
      mockLog("TokenService", "createToken", tokenData)
      await createMockDelay(MOCK_DELAYS.NORMAL)

      const newMockToken: TokenResponse = {
        id: getNextMockTokenId(),
        user_id: 1, // Assuming current user ID is 1 for mocks
        key: `sk-mock${Date.now().toString().slice(-10)}`,
        name: tokenData.name,
        status: 1,
        status_text: "Active",
        group: tokenData.group || "default",
        remain_quota: tokenData.remain_quota === undefined ? 0 : tokenData.remain_quota,
        used_quota: 0,
        unlimited_quota: tokenData.unlimited_quota || false,
        model_limits_enabled: tokenData.model_limits_enabled || false,
        model_limits: tokenData.model_limits || [],
        allow_ips: tokenData.allow_ips || [],
        expired_time: tokenData.expired_time === undefined ? -1 : tokenData.expired_time,
        request_count: 0,
        last_used_time: null,
        created_time: new Date().toISOString(),
      }

      MockDataManager.addToken(newMockToken)
      return Promise.resolve({ data: newMockToken })
    }
    return this.client.request<TokenResponse>("/api/v1/tokens", {
      method: "POST",
      body: JSON.stringify(tokenData),
    })
  }

  async getTokens(
    params: {
      page?: number
      per_page?: number
      status?: number
    } = {},
  ): Promise<ApiResponse<TokenResponse[]>> {
    // Changed to TokenResponse[] for items
    if (this.useMocks) {
      mockLog("TokenService", "getTokens", params)
      await createMockDelay(MOCK_DELAYS.NORMAL)

      // Simple mock pagination/filtering
      let filteredTokens = MockDataManager.getTokens()
      if (params.status !== undefined) {
        filteredTokens = filteredTokens.filter((t) => t.status === params.status)
      }
      const page = params.page || 1
      const perPage = params.per_page || 10
      const start = (page - 1) * perPage
      const end = start + perPage
      const paginatedTokens = filteredTokens.slice(start, end)

      return Promise.resolve({
        data: paginatedTokens, // Use 'data' for ApiResponse format
        items: paginatedTokens, // Use 'items' as per OpenAPI spec for lists
        total: filteredTokens.length,
        page: page,
        per_page: perPage,
        has_next: end < filteredTokens.length,
        has_prev: start > 0,
      } as any)
    }
    const queryString = new URLSearchParams(
      Object.entries(params)
        .filter(([_, v]) => v !== undefined)
        .map(([k, v]) => [k, String(v)]),
    ).toString()
    // The API returns items in `items` field for paginated lists
    return this.client.request<TokenResponse[]>(`/api/v1/tokens?${queryString}`)
  }

  async updateToken(tokenId: number, updateData: TokenUpdateRequest): Promise<ApiResponse<TokenResponse>> {
    if (this.useMocks) {
      mockLog("TokenService", "updateToken", { tokenId, updateData })
      await createMockDelay(MOCK_DELAYS.NORMAL)

      const updateWithStatusText = {
        ...updateData,
        status_text: updateData.status === 1 ? "Active" : "Inactive",
      }

      const success = MockDataManager.updateToken(tokenId, updateWithStatusText)
      if (success) {
        const tokens = MockDataManager.getTokens()
        const updatedToken = tokens.find(t => t.id === tokenId)
        return Promise.resolve({ data: updatedToken! })
      }
      return Promise.reject(new Error("Mock token not found"))
    }
    return this.client.request<TokenResponse>(`/api/v1/tokens/${tokenId}`, {
      method: "PUT",
      body: JSON.stringify(updateData),
    })
  }

  async deleteToken(tokenId: number): Promise<ApiResponse<null>> {
    if (this.useMocks) {
      mockLog("TokenService", "deleteToken", { tokenId })
      await createMockDelay(MOCK_DELAYS.NORMAL)

      const success = MockDataManager.deleteToken(tokenId)
      if (success) {
        return Promise.resolve({ message: "Token deleted successfully" } as unknown as ApiResponse<null>)
      }
      return Promise.reject(new Error("Mock token not found"))
    }
    return this.client.request<null>(`/api/v1/tokens/${tokenId}`, {
      method: "DELETE",
    })
  }
}

export const tokenService = new TokenService(apiClient)
