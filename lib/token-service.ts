import { apiClient, type TaskAPIClient, type ApiResponse } from "./api-client"
import type {
  TokenCreateRequest,
  TokenResponse,
  TokenListResponse as ApiTokenListResponse,
  TokenUpdateRequest,
} from "./api-types"

// Mock data for tokens
let mockTokens: TokenResponse[] = [
  {
    id: 1,
    user_id: 1,
    key: "sk-mocktoken123abc",
    name: "My First Mock Token",
    status: 1,
    status_text: "Active",
    group: "default",
    remain_quota: 10000,
    used_quota: 500,
    unlimited_quota: false,
    model_limits_enabled: false,
    model_limits: [],
    allow_ips: [],
    expired_time: -1,
    request_count: 120,
    last_used_time: new Date(Date.now() - 86400000).toISOString(), // Yesterday
    created_time: new Date(Date.now() - 86400000 * 7).toISOString(), // Last week
  },
  {
    id: 2,
    user_id: 1,
    key: "sk-mocktoken789xyz",
    name: "Unlimited Project Token",
    status: 1,
    status_text: "Active",
    group: "project-x",
    remain_quota: 0,
    used_quota: 15000,
    unlimited_quota: true,
    model_limits_enabled: true,
    model_limits: ["whisper-1", "gpt-4"],
    allow_ips: ["*************"],
    expired_time: new Date(Date.now() + 86400000 * 30).toISOString(), // Expires in 30 days
    request_count: 500,
    last_used_time: new Date().toISOString(),
    created_time: new Date(Date.now() - 86400000 * 2).toISOString(), // Two days ago
  },
]
let nextMockTokenId = 3

export interface TokenListResponse extends ApiTokenListResponse {
  // The API spec uses 'tokens', but the guide uses 'tokens'. Let's align with the guide's component.
  // The actual API response has 'items' for the list. We'll map it.
}

export class TokenService {
  private client: TaskAPIClient
  private useMocks: boolean

  constructor(client: TaskAPIClient, useMocks = true) {
    this.client = client
    this.useMocks = useMocks
  }

  async createToken(tokenData: TokenCreateRequest): Promise<ApiResponse<TokenResponse>> {
    if (this.useMocks) {
      console.log("Mock createToken:", tokenData)
      const newMockToken: TokenResponse = {
        id: nextMockTokenId++,
        user_id: 1, // Assuming current user ID is 1 for mocks
        key: `sk-mock${Date.now().toString().slice(-10)}`,
        name: tokenData.name,
        status: 1,
        status_text: "Active",
        group: tokenData.group || "default",
        remain_quota: tokenData.remain_quota === undefined ? 0 : tokenData.remain_quota,
        used_quota: 0,
        unlimited_quota: tokenData.unlimited_quota || false,
        model_limits_enabled: tokenData.model_limits_enabled || false,
        model_limits: tokenData.model_limits || [],
        allow_ips: tokenData.allow_ips || [],
        expired_time: tokenData.expired_time === undefined ? -1 : tokenData.expired_time,
        request_count: 0,
        last_used_time: null,
        created_time: new Date().toISOString(),
      }
      mockTokens.push(newMockToken)
      return Promise.resolve({ data: newMockToken })
    }
    return this.client.request<TokenResponse>("/api/v1/tokens", {
      method: "POST",
      body: JSON.stringify(tokenData),
    })
  }

  async getTokens(
    params: {
      page?: number
      per_page?: number
      status?: number
    } = {},
  ): Promise<ApiResponse<TokenResponse[]>> {
    // Changed to TokenResponse[] for items
    if (this.useMocks) {
      console.log("Mock getTokens:", params)
      // Simple mock pagination/filtering
      let filteredTokens = mockTokens
      if (params.status !== undefined) {
        filteredTokens = filteredTokens.filter((t) => t.status === params.status)
      }
      const page = params.page || 1
      const perPage = params.per_page || 10
      const start = (page - 1) * perPage
      const end = start + perPage
      const paginatedTokens = filteredTokens.slice(start, end)

      return Promise.resolve({
        items: paginatedTokens, // Use 'items' as per OpenAPI spec for lists
        total: filteredTokens.length,
        page: page,
        per_page: perPage,
        has_next: end < filteredTokens.length,
        has_prev: start > 0,
      })
    }
    const queryString = new URLSearchParams(
      Object.entries(params)
        .filter(([_, v]) => v !== undefined)
        .map(([k, v]) => [k, String(v)]),
    ).toString()
    // The API returns items in `items` field for paginated lists
    return this.client.request<TokenResponse[]>(`/api/v1/tokens?${queryString}`)
  }

  async updateToken(tokenId: number, updateData: TokenUpdateRequest): Promise<ApiResponse<TokenResponse>> {
    if (this.useMocks) {
      console.log("Mock updateToken:", tokenId, updateData)
      const tokenIndex = mockTokens.findIndex((t) => t.id === tokenId)
      if (tokenIndex > -1) {
        mockTokens[tokenIndex] = {
          ...mockTokens[tokenIndex],
          ...updateData,
          status_text: updateData.status === 1 ? "Active" : "Inactive",
        }
        return Promise.resolve({ data: mockTokens[tokenIndex] })
      }
      return Promise.reject(new Error("Mock token not found"))
    }
    return this.client.request<TokenResponse>(`/api/v1/tokens/${tokenId}`, {
      method: "PUT",
      body: JSON.stringify(updateData),
    })
  }

  async deleteToken(tokenId: number): Promise<ApiResponse<null>> {
    if (this.useMocks) {
      console.log("Mock deleteToken:", tokenId)
      mockTokens = mockTokens.filter((t) => t.id !== tokenId)
      return Promise.resolve({ message: "Token deleted successfully" } as unknown as ApiResponse<null>)
    }
    return this.client.request<null>(`/api/v1/tokens/${tokenId}`, {
      method: "DELETE",
    })
  }
}

export const tokenService = new TokenService(apiClient, process.env.NEXT_PUBLIC_USE_MOCKS !== "false")
