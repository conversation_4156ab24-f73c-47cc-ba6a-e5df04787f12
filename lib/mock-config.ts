/**
 * Mock配置管理
 * 统一管理Mock数据的开关和相关配置
 */

/**
 * 判断是否使用Mock数据
 * @returns {boolean} true表示使用Mock数据，false表示使用真实API
 */
export function shouldUseMocks(): boolean {
  // 默认启用Mock，只有明确设置为 "false" 时才禁用
  return process.env.NEXT_PUBLIC_USE_MOCKS !== "false"
}

/**
 * 获取Mock配置信息
 * @returns {object} Mock配置对象
 */
export function getMockConfig() {
  return {
    enabled: shouldUseMocks(),
    apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000",
    debug: process.env.NEXT_PUBLIC_DEBUG === "true",
    timeout: parseInt(process.env.NEXT_PUBLIC_API_TIMEOUT || "10000", 10),
  }
}

/**
 * Mock延迟配置
 * 用于模拟网络请求的延迟时间
 */
export const MOCK_DELAYS = {
  // 快速响应 (如获取缓存数据)
  FAST: 200,
  // 正常响应 (如普通API调用)
  NORMAL: 800,
  // 慢速响应 (如文件上传、复杂计算)
  SLOW: 2000,
  // 处理中状态变化的间隔
  PROCESSING_INTERVAL: 3000,
} as const

/**
 * Mock状态配置
 * 定义各种Mock场景的状态
 */
export const MOCK_STATUS = {
  SUCCESS: "success",
  ERROR: "error",
  PENDING: "pending",
  PROCESSING: "processing",
  COMPLETED: "completed",
  FAILED: "failed",
} as const

/**
 * 创建Mock延迟Promise
 * @param delay 延迟时间（毫秒）
 * @returns Promise
 */
export function createMockDelay(delay: number = MOCK_DELAYS.NORMAL): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, delay))
}

/**
 * Mock日志输出
 * 在开发环境下输出Mock相关的调试信息
 * @param service 服务名称
 * @param action 操作名称
 * @param data 相关数据
 */
export function mockLog(service: string, action: string, data?: any): void {
  if (process.env.NEXT_PUBLIC_DEBUG === "true" && shouldUseMocks()) {
    console.log(`[Mock ${service}] ${action}`, data || "")
  }
}

/**
 * Mock错误生成器
 * 用于生成各种Mock错误场景
 */
export const MockErrors = {
  NETWORK_ERROR: new Error("Mock network error: Connection failed"),
  UNAUTHORIZED: new Error("Mock auth error: Invalid credentials"),
  FORBIDDEN: new Error("Mock auth error: Access denied"),
  NOT_FOUND: new Error("Mock error: Resource not found"),
  VALIDATION_ERROR: new Error("Mock validation error: Invalid input"),
  SERVER_ERROR: new Error("Mock server error: Internal server error"),
} as const

/**
 * 随机Mock场景生成器
 * 用于模拟不同的成功/失败场景
 */
export class MockScenario {
  /**
   * 根据概率返回成功或失败
   * @param successRate 成功率 (0-1)
   * @returns boolean
   */
  static shouldSucceed(successRate: number = 0.9): boolean {
    return Math.random() < successRate
  }

  /**
   * 随机选择一个错误
   * @returns Error
   */
  static getRandomError(): Error {
    const errors = Object.values(MockErrors)
    return errors[Math.floor(Math.random() * errors.length)]
  }

  /**
   * 模拟网络不稳定的延迟
   * @param baseDelay 基础延迟时间
   * @param variance 变化范围 (0-1)
   * @returns number
   */
  static getRandomDelay(baseDelay: number = MOCK_DELAYS.NORMAL, variance: number = 0.3): number {
    const minDelay = baseDelay * (1 - variance)
    const maxDelay = baseDelay * (1 + variance)
    return Math.floor(Math.random() * (maxDelay - minDelay) + minDelay)
  }
}
