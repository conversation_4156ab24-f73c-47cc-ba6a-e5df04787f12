import { apiClient, type Task<PERSON><PERSON>lient, type ApiR<PERSON>ponse } from "./api-client"
import type {
  UserRegisterRequest,
  UserLoginRequest,
  TokenResponse as AuthTokenResponse,
  UserResponse as AuthUserResponse,
} from "./api-types" // Assuming api-types.ts exists

// Mock user data for login
const MOCK_USER_LOGIN_SUCCESS: AuthTokenResponse = {
  access_token: "mock_jwt_token_12345",
  token_type: "bearer",
  expires_in: 3600,
  user_id: 1,
  username: "testuser",
  role: 1, // 1 for normal user, 100 for admin
}

const MOCK_ADMIN_LOGIN_SUCCESS: AuthTokenResponse = {
  access_token: "mock_admin_jwt_token_67890",
  token_type: "bearer",
  expires_in: 3600,
  user_id: 99, // Different user_id for admin
  username: "adminuser",
  role: 100, // Admin role
}

const MOCK_USER_REGISTER_SUCCESS = {
  message: "Registration successful",
  user_id: 2,
  verification_required: false,
}

const MOCK_CURRENT_USER: AuthUserResponse = {
  id: 1,
  username: "testuser",
  display_name: "Test User",
  email: "<EMAIL>",
  role: 1,
  status: 1,
  group: "default",
  quota: 10000,
  used_quota: 500,
  request_count: 120,
  created_time: new Date().toISOString(),
  last_login_time: new Date().toISOString(),
  aff_code: "AFF123",
  aff_quota: 500,
}

export class AuthService {
  private client: TaskAPIClient
  private useMocks: boolean

  constructor(client: TaskAPIClient, useMocks = true) {
    // Default to true for easy testing
    this.client = client
    this.useMocks = useMocks
  }

  async register(
    userData: UserRegisterRequest,
  ): Promise<ApiResponse<{ user_id: number; verification_required: boolean; message: string }>> {
    if (this.useMocks) {
      console.log("Mock register:", userData)
      return Promise.resolve({ data: MOCK_USER_REGISTER_SUCCESS, message: "Registration successful" })
    }
    return this.client.request("/api/v1/auth/register", {
      method: "POST",
      body: JSON.stringify(userData),
    })
  }

  async login(credentials: UserLoginRequest): Promise<ApiResponse<AuthTokenResponse>> {
    if (this.useMocks) {
      console.log("Mock login:", credentials)
      if (credentials.username === "testuser" && credentials.password === "password") {
        this.client.setJWTToken(MOCK_USER_LOGIN_SUCCESS.access_token)
        if (typeof window !== "undefined") {
          localStorage.setItem(
            "user_info",
            JSON.stringify({
              user_id: MOCK_USER_LOGIN_SUCCESS.user_id,
              username: MOCK_USER_LOGIN_SUCCESS.username,
              role: MOCK_USER_LOGIN_SUCCESS.role,
            }),
          )
        }
        return Promise.resolve({ data: MOCK_USER_LOGIN_SUCCESS })
      } else if (credentials.username === "adminuser" && credentials.password === "adminpassword") {
        this.client.setJWTToken(MOCK_ADMIN_LOGIN_SUCCESS.access_token)
        if (typeof window !== "undefined") {
          localStorage.setItem(
            "user_info",
            JSON.stringify({
              user_id: MOCK_ADMIN_LOGIN_SUCCESS.user_id,
              username: MOCK_ADMIN_LOGIN_SUCCESS.username,
              role: MOCK_ADMIN_LOGIN_SUCCESS.role,
            }),
          )
        }
        return Promise.resolve({ data: MOCK_ADMIN_LOGIN_SUCCESS })
      } else {
        return Promise.reject(new Error("Invalid mock credentials"))
      }
    }

    const response = await this.client.request<AuthTokenResponse>("/api/v1/auth/login", {
      method: "POST",
      body: JSON.stringify(credentials),
    })

    if (response.data?.access_token) {
      this.client.setJWTToken(response.data.access_token)
      if (typeof window !== "undefined") {
        localStorage.setItem(
          "user_info",
          JSON.stringify({
            user_id: response.data.user_id,
            username: response.data.username,
            role: response.data.role,
          }),
        )
      }
    }
    return response
  }

  async logout() {
    if (this.useMocks) {
      console.log("Mock logout")
      this.client.clearAuth()
      return Promise.resolve({ message: "Logout successful" })
    }
    // Actual logout might involve an API call to invalidate token server-side if supported
    // For now, just clear client-side
    this.client.clearAuth()
    return Promise.resolve({ message: "Logout successful" })
  }

  isAuthenticated(): boolean {
    if (this.useMocks) {
      return !!this.client.getJWTToken()
    }
    return !!this.client.getJWTToken() // Or check localStorage directly
  }

  getCurrentUser(): Partial<AuthUserResponse> | null {
    if (typeof window !== "undefined") {
      const userInfo = localStorage.getItem("user_info")
      return userInfo ? JSON.parse(userInfo) : null
    }
    return null
  }

  async getCurrentUserInfo(): Promise<ApiResponse<AuthUserResponse>> {
    if (this.useMocks) {
      console.log("Mock getCurrentUserInfo")
      if (this.isAuthenticated()) {
        return Promise.resolve({ data: MOCK_CURRENT_USER })
      }
      return Promise.reject(new Error("Not authenticated"))
    }
    return this.client.request<AuthUserResponse>("/api/v1/auth/me")
  }
}

export const authService = new AuthService(apiClient, process.env.NEXT_PUBLIC_USE_MOCKS !== "false")
