import { apiClient, type Task<PERSON><PERSON>lient, type ApiR<PERSON>ponse } from "./api-client"
import type {
  UserRegisterRequest,
  UserLoginRequest,
  AuthTokenResponse,
  UserResponse as AuthUserResponse,
} from "./api-types"
import { shouldUseMocks, mockLog, createMockDelay, MOCK_DELAYS } from "./mock-config"
import {
  MOCK_USER_LOGIN_SUCCESS,
  MOCK_ADMIN_LOGIN_SUCCESS,
  MOCK_USER_REGISTER_SUCCESS,
  MOCK_CURRENT_USER,
} from "./mock-data"

export class AuthService {
  private client: TaskAPIClient
  private useMocks: boolean

  constructor(client: TaskAPIClient, useMocks?: boolean) {
    this.client = client
    // 使用统一的Mock配置判断逻辑
    this.useMocks = useMocks !== undefined ? useMocks : shouldUseMocks()
  }

  async register(
    userData: UserRegisterRequest,
  ): Promise<ApiResponse<{ user_id: number; verification_required: boolean; message: string }>> {
    if (this.useMocks) {
      mockLog("AuthService", "register", userData)
      await createMockDelay(MOCK_DELAYS.NORMAL)
      return Promise.resolve({ data: MOCK_USER_REGISTER_SUCCESS, message: "Registration successful" })
    }
    return this.client.request("/api/v1/auth/register", {
      method: "POST",
      body: JSON.stringify(userData),
    })
  }

  async login(credentials: UserLoginRequest): Promise<ApiResponse<AuthTokenResponse>> {
    if (this.useMocks) {
      mockLog("AuthService", "login", { username: credentials.username })
      await createMockDelay(MOCK_DELAYS.NORMAL)

      if (credentials.username === "testuser" && credentials.password === "password") {
        this.client.setJWTToken(MOCK_USER_LOGIN_SUCCESS.access_token)
        if (typeof window !== "undefined") {
          localStorage.setItem(
            "user_info",
            JSON.stringify({
              user_id: MOCK_USER_LOGIN_SUCCESS.user_id,
              username: MOCK_USER_LOGIN_SUCCESS.username,
              role: MOCK_USER_LOGIN_SUCCESS.role,
            }),
          )
        }
        return Promise.resolve({ data: MOCK_USER_LOGIN_SUCCESS })
      } else if (credentials.username === "adminuser" && credentials.password === "adminpassword") {
        this.client.setJWTToken(MOCK_ADMIN_LOGIN_SUCCESS.access_token)
        if (typeof window !== "undefined") {
          localStorage.setItem(
            "user_info",
            JSON.stringify({
              user_id: MOCK_ADMIN_LOGIN_SUCCESS.user_id,
              username: MOCK_ADMIN_LOGIN_SUCCESS.username,
              role: MOCK_ADMIN_LOGIN_SUCCESS.role,
            }),
          )
        }
        return Promise.resolve({ data: MOCK_ADMIN_LOGIN_SUCCESS })
      } else {
        return Promise.reject(new Error("Invalid mock credentials"))
      }
    }

    const response = await this.client.request<AuthTokenResponse>("/api/v1/auth/login", {
      method: "POST",
      body: JSON.stringify(credentials),
    })

    if (response.data?.access_token) {
      this.client.setJWTToken(response.data.access_token)
      if (typeof window !== "undefined") {
        localStorage.setItem(
          "user_info",
          JSON.stringify({
            user_id: response.data.user_id,
            username: response.data.username,
            role: response.data.role,
          }),
        )
      }
    }
    return response
  }

  async logout() {
    if (this.useMocks) {
      mockLog("AuthService", "logout")
      await createMockDelay(MOCK_DELAYS.FAST)
      this.client.clearAuth()
      return Promise.resolve({ message: "Logout successful" })
    }
    // Actual logout might involve an API call to invalidate token server-side if supported
    // For now, just clear client-side
    this.client.clearAuth()
    return Promise.resolve({ message: "Logout successful" })
  }

  isAuthenticated(): boolean {
    if (this.useMocks) {
      return !!this.client.getJWTToken()
    }
    return !!this.client.getJWTToken() // Or check localStorage directly
  }

  getCurrentUser(): Partial<AuthUserResponse> | null {
    if (typeof window !== "undefined") {
      const userInfo = localStorage.getItem("user_info")
      return userInfo ? JSON.parse(userInfo) : null
    }
    return null
  }

  async getCurrentUserInfo(): Promise<ApiResponse<AuthUserResponse>> {
    if (this.useMocks) {
      mockLog("AuthService", "getCurrentUserInfo")
      await createMockDelay(MOCK_DELAYS.FAST)
      if (this.isAuthenticated()) {
        return Promise.resolve({ data: MOCK_CURRENT_USER })
      }
      return Promise.reject(new Error("Not authenticated"))
    }
    return this.client.request<AuthUserResponse>("/api/v1/auth/me")
  }
}

export const authService = new AuthService(apiClient)
